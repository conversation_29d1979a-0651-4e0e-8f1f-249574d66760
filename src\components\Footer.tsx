"use client";

import React from "react";
import { Facebook, Twitter, Youtube } from "react-feather";
import FooterMenu from "@/components/Widget/FooterMenu";
import { usePathname } from "next/navigation";
import { useSetting } from "@/context/SettingContext";
import Logo from "./Navigation/Logo";

const Footer = () => {
  const { setting, menus } = useSetting();
  const pathname = usePathname();
  const isPrivateRoute = pathname.startsWith("/dashboard");
  const menuPosition7 = menus?.find((menu: any) => menu.position === "7");
  
  return (
    <>
      {!isPrivateRoute && (
        <footer className="mx-auto border-t border-gray-300">
          {/* <div className="grid grid-cols-3 gap-4 md:grid-cols-6 mb-4 border-b py-8">
            {["1", "2", "3", "4", "5", "6"].map((position) => (
              <FooterMenu key={position} position={position} />
            ))}
          </div> */}
          
          {/* Logo and Menu Section */}
          <div className="bg-[#e4393c] text-white">
            <div className="container mx-auto px-6 py-8">
              <div className="copyright flex flex-wrap items-center justify-between">
                <div className="mb-4 md:mb-0">
                  <div className="flex items-center">
                    <div className="mr-3">
                      <img src="/favicon.ico" alt="Quốc huy" className="w-12 h-12" />
                    </div>
                    <div className="text-left">
                      <div className="text-sm text-blue-400 font-medium">CỔNG THÔNG TIN ĐIỆN TỬ</div>
                      <div className="text-xl font-bold">TÒA ÁN NHÂN DÂN THÀNH PHỐ HỒ CHÍ MINH</div>
                    </div>
                  </div>
                </div>
                
                {menuPosition7 && (
                  <div className="right flex flex-wrap gap-4">
                    {menuPosition7.obj.map((item: any) => (
                      <a
                        key={item.id}
                        href={`/${item.slug}`}
                        className="hover:text-blue-200 transition-colors"
                      >
                        {item.text}
                      </a>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
          
          {/* Footer Blocks Section */}
          <div className="bg-[#ca1619]">
            <div className="container mx-auto px-6 py-8">
              <div className="copyright-footer grid md:grid-cols-3 grid-cols-1 gap-8">
                {setting?.footerBLock1 && (
                  <div className="text-white font-normal">
                    <div
                      className="footer-content"
                      dangerouslySetInnerHTML={{ __html: setting.footerBLock1 }}
                    />
                  </div>
                )}
                
                {setting?.footerBLock2 && (
                  <div className="text-white font-normal">
                    <div
                      className="footer-content"
                      dangerouslySetInnerHTML={{ __html: setting.footerBLock2 }}
                    />
                  </div>
                )}
                
                <div className="flex items-center justify-start md:justify-end">
                  <p className="text-white font-normal">
                    {setting?.copyright || "© Your Website Name"}
                  </p>
                </div>
              </div>
            </div>
          </div>
          
        </footer>
      )}
    </>
  );
};

export default Footer;
