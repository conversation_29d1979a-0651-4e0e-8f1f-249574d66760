"use client";

import { useState } from "react";
import { ExternalLink, Download, Eye, EyeOff } from "lucide-react";

interface PdfOnlineViewerProps {
  fileUrl: string;
  fileName?: string;
}

function PdfOnlineViewer({ fileUrl, fileName = "document.pdf" }: PdfOnlineViewerProps) {
  const [showOnlineViewer, setShowOnlineViewer] = useState<boolean>(false);
  const [currentViewer, setCurrentViewer] = useState<string>("google");

  const viewers = {
    google: {
      name: "Google Docs Viewer",
      url: `https://docs.google.com/viewer?url=${encodeURIComponent(fileUrl)}&embedded=true`,
      description: "Xem PDF qua Google Docs Viewer"
    },
    mozilla: {
      name: "Mozilla PDF.js",
      url: `https://mozilla.github.io/pdf.js/web/viewer.html?file=${encodeURIComponent(fileUrl)}`,
      description: "Xem PDF qua Mozilla PDF.js"
    },
    office: {
      name: "Microsoft Office Online",
      url: `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(fileUrl)}`,
      description: "Xem PDF qua Microsoft Office Online"
    }
  };

  const toggleOnlineViewer = () => {
    setShowOnlineViewer(!showOnlineViewer);
  };

  const changeViewer = (viewerKey: string) => {
    setCurrentViewer(viewerKey);
  };

  return (
    <div className="w-full border rounded-lg shadow-lg bg-white overflow-hidden">
      {/* Header */}
      <div className="bg-gray-100 border-b px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <h3 className="font-medium text-gray-900">Xem PDF Online</h3>
            <span className="text-sm text-gray-500">({fileName})</span>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={toggleOnlineViewer}
              className={`flex items-center space-x-2 px-3 py-1 rounded text-sm transition-colors ${
                showOnlineViewer 
                  ? 'bg-green-600 text-white hover:bg-green-700' 
                  : 'bg-blue-600 text-white hover:bg-blue-700'
              }`}
            >
              {showOnlineViewer ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
              <span>{showOnlineViewer ? 'Ẩn' : 'Hiển thị'} Viewer</span>
            </button>
            <a
              href={fileUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center space-x-2 px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors text-sm"
            >
              <Download className="w-4 h-4" />
              <span>Tải xuống</span>
            </a>
          </div>
        </div>

        {/* Viewer Selection */}
        {showOnlineViewer && (
          <div className="mt-3 border-t pt-3">
            <div className="flex flex-wrap gap-2">
              {Object.entries(viewers).map(([key, viewer]) => (
                <button
                  key={key}
                  onClick={() => changeViewer(key)}
                  className={`px-3 py-1 rounded text-xs transition-colors ${
                    currentViewer === key 
                      ? 'bg-blue-600 text-white' 
                      : 'bg-white border text-gray-700 hover:bg-gray-50'
                  }`}
                  title={viewer.description}
                >
                  {viewer.name}
                </button>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Online Viewer Content */}
      {showOnlineViewer && (
        <div className="relative">
          <iframe
            src={viewers[currentViewer as keyof typeof viewers].url}
            width="100%"
            height="600"
            frameBorder="0"
            className="border-0"
            title={`PDF Viewer - ${viewers[currentViewer as keyof typeof viewers].name}`}
            sandbox="allow-scripts allow-same-origin allow-popups allow-forms"
          />
          
          {/* Fallback message */}
          <div className="absolute inset-0 flex items-center justify-center bg-gray-50 opacity-0 hover:opacity-100 transition-opacity pointer-events-none">
            <div className="text-center p-4">
              <p className="text-sm text-gray-600 mb-2">
                Nếu PDF không hiển thị, hãy thử:
              </p>
              <div className="space-y-1 text-xs text-gray-500">
                <p>• Chuyển đổi viewer bằng các nút bên trên</p>
                <p>• Tải xuống file để xem offline</p>
                <p>• Mở link trực tiếp trong tab mới</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Quick Links */}
      <div className="bg-gray-50 border-t px-4 py-3">
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-600">
            Xem trực tuyến hoặc tải xuống để trải nghiệm tốt nhất
          </span>
          <div className="flex items-center space-x-3">
            <a
              href={viewers.google.url}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center space-x-1 text-blue-600 hover:text-blue-800 transition-colors"
            >
              <ExternalLink className="w-3 h-3" />
              <span>Mở trong tab mới</span>
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}

export default PdfOnlineViewer;
