import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    return NextResponse.json({
      success: true,
      message: "Analytics API is working!",
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      message: "Analytics API test failed",
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
