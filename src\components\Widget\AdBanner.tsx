"use client";

import React, { Suspense } from "react";
import { useSetting } from "@/context/SettingContext";
import { FadeIn } from "@/components/Widget/AnimatedElements";

const AdContent = () => {
  const { setting } = useSetting();

  // Don't render anything if there's no ad content
  if (!setting?.ads1 || setting.ads1.trim() === '') {
    return null;
  }

  return (
    <FadeIn delay={200}>
      <aside className="mb-4 sm:mb-6 transform transition-all duration-500 hover:scale-[1.02]" aria-label="Quảng cáo">
        <div className="bg-white/90 backdrop-blur-sm rounded-xl shadow-lg p-2 overflow-hidden">
          <div
            dangerouslySetInnerHTML={{ __html: setting.ads1 }}
            className="ads-content"
          />
        </div>
      </aside>
    </FadeIn>
  );
};

const AdBanner = () => {
  return (
    <Suspense fallback={null}>
      <AdContent />
    </Suspense>
  );
};

export default AdBanner;
