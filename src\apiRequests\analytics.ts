import http from "@/lib/http";

export interface AnalyticsOverview {
  totalViewsAllTime: number;
  totalViewsThisMonth: number;
  totalViewsThisWeek: number;
  totalViewsToday: number;
  homeViewsToday: number;
  postViewsToday: number;
  uniqueVisitorsToday: number;
  homeViewsGrowth: number;
  postViewsGrowth: number;
}

export interface TopPost {
  postId: string;
  views: number;
  title: string;
  slug: string;
}

export interface AnalyticsOverviewResponse {
  success: boolean;
  analytics: {
    overview: AnalyticsOverview;
    topPostsToday: TopPost[];
  };
}

export interface ChartDataPoint {
  date: string;
  homeViews: number;
  postViews: number;
  totalViews: number;
  uniqueVisitors: number;
}

export interface ChartDataResponse {
  success: boolean;
  chartData: ChartDataPoint[];
}

const analyticsApiRequest = {
  getOverview: (sessionToken: string) =>
    http.get<AnalyticsOverviewResponse>("/api/analytics/overview", {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  getChartData: (sessionToken: string, days: number = 7) =>
    http.get<ChartDataResponse>(`/api/analytics/chart-data?days=${days}`, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  trackPageView: (data: {
    page: string;
    path: string;
    postId?: string;
    sessionId: string;
    userAgent: string;
    referer: string;
  }) =>
    http.post("/api/analytics/track", data),

  trackEvent: (data: {
    eventType: string;
    eventData?: any;
    sessionId: string;
    path: string;
  }) =>
    http.post("/api/analytics/event", data),
};

export default analyticsApiRequest;
