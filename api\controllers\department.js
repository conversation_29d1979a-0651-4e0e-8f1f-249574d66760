const Leader = require("../models/leader");
const Department = require("../models/department");
const Position = require("../models/position");

// Helper function to normalize department levels (remove gaps)
const normalizeDepartmentLevels = async () => {
  try {
    // Get all departments sorted by current level
    const departments = await Department.find({}).sort({ level: 1, createdAt: 1 });
    
    // Reassign levels starting from 1 without gaps
    for (let i = 0; i < departments.length; i++) {
      const newLevel = i + 1;
      if (departments[i].level !== newLevel) {
        departments[i].level = newLevel;
        await departments[i].save();
        // Console log removed
      }
    }
    
    return departments;
  } catch (error) {
    // Console error removed
    throw error;
  }
};

// Get all departments (from both Department model and leaders)
const getAllDepartments = async (req, res) => {
  try {
    // Get departments from Department model, sorted by level (lowest first)
    const storedDepartments = await Department.find({}).sort({ level: 1, createdAt: 1 });

    // Get unique departments from leaders (for backward compatibility)
    // Since leaders now use ObjectId references, we need to populate the department data
    const leadersWithDepartments = await Leader.find({}).populate('department', 'name level').select('department');

    // Combine and deduplicate departments
    const allDepartmentNames = new Set();
    const departmentMap = new Map(); // Store department data including level

    // Add stored departments
    storedDepartments.forEach(dept => {
      allDepartmentNames.add(dept.name);
      departmentMap.set(dept.name, {
        _id: dept._id.toString(),
        name: dept.name,
        level: dept.level || 5 // Default level if not set
      });
    });

    // Add leader departments (for any that might not be in Department model)
    leadersWithDepartments.forEach(leader => {
      if (leader.department) {
        let deptName = null;
        let deptLevel = 5; // Default level

        // Handle both string (old format) and object (new format) departments
        if (typeof leader.department === 'string') {
          deptName = leader.department.trim();
        } else if (leader.department && typeof leader.department === 'object' && leader.department.name) {
          deptName = leader.department.name;
          deptLevel = leader.department.level || 5;
        }

        if (deptName && typeof deptName === 'string' && !departmentMap.has(deptName)) {
          allDepartmentNames.add(deptName);
          departmentMap.set(deptName, {
            _id: `temp_${departmentMap.size}`,
            name: deptName,
            level: deptLevel
          });
        }
      }
    });

    // Create final department list sorted by level (lowest first)
    const departmentList = Array.from(departmentMap.values()).sort((a, b) => {
      if (a.level !== b.level) {
        return a.level - b.level; // Sort by level ascending (lowest first)
      }
      return a.name.localeCompare(b.name); // Then by name alphabetically
    });

    res.status(200).json({
      success: true,
      data: departmentList,
      message: "Lấy danh sách phòng ban thành công"
    });
  } catch (error) {
    // Console error removed
    res.status(500).json({
      success: false,
      message: "Lỗi server khi lấy danh sách phòng ban"
    });
  }
};

// Create new department (store in Department model)
const createDepartment = async (req, res) => {
  try {
    const { name } = req.body;

    if (!name || !name.trim()) {
      return res.status(400).json({
        success: false,
        message: "Tên phòng ban không được để trống"
      });
    }

    // Check if department already exists in Department model
    const existingDepartment = await Department.findOne({ name: name.trim() });
    if (existingDepartment) {
      return res.status(409).json({
        success: false,
        message: "Phòng ban đã tồn tại"
      });
    }

    // Check if department exists in leaders (for backward compatibility)
    // Only check string department fields to avoid casting errors with ObjectId fields
    let existingInLeaders = null;
    try {
      existingInLeaders = await Leader.findOne({
        department: { $type: "string", $eq: name.trim() }
      });
    } catch (error) {
      // Ignore errors from ObjectId casting - this is expected for newer records
      // Console log removed
    }

    if (existingInLeaders) {
      return res.status(409).json({
        success: false,
        message: "Phòng ban đã tồn tại"
      });
    }

    // Find the highest level to place new department at the end
    const highestLevelDept = await Department.findOne().sort({ level: -1 });
    const newLevel = highestLevelDept ? highestLevelDept.level + 1 : 1;

    // Create new department with level at the end
    const newDepartment = new Department({
      name: name.trim(),
      level: newLevel
    });

    const savedDepartment = await newDepartment.save();

    // Do not create default positions anymore

    res.status(201).json({
      success: true,
      data: {
        id: savedDepartment._id.toString(),
        name: savedDepartment.name,
        level: savedDepartment.level
      },
      message: "Phòng ban được tạo thành công"
    });
  } catch (error) {
    // Console error removed
    if (error.code === 11000) {
      return res.status(409).json({
        success: false,
        message: "Phòng ban đã tồn tại"
      });
    }
    res.status(500).json({
      success: false,
      message: "Lỗi server khi tạo phòng ban"
    });
  }
};

// Update department name and level (update both Department model and all leaders)
const updateDepartment = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, level } = req.body;

    // Console log removed

    if (!name || !name.trim()) {
      // Console log removed
      return res.status(400).json({
        success: false,
        message: "Tên phòng ban không được để trống"
      });
    }

    // Try to find department in Department model first
    // Console log removed
    const existingDepartment = await Department.findById(id);
    // Console log removed
    let oldName = null;

    if (existingDepartment) {
      oldName = existingDepartment.name;
      // Console log removed
    } else {
      // Console log removed
      // Fallback: try to find by index in leaders (for backward compatibility)
      const departments = await Leader.distinct('department');
      // Console log removed
      oldName = departments[parseInt(id)] || departments.find(d => d && d.includes(name));
      // Console log removed
    }

    if (!oldName) {
      // Console log removed
      return res.status(404).json({
        success: false,
        message: "Không tìm thấy phòng ban"
      });
    }

    // Check if new name already exists (and different from old name)
    if (oldName !== name.trim()) {
      // Console log removed
      const existingByName = await Department.findOne({ name: name.trim() });
      // Console log removed

      // For leaders, we need to be careful about ObjectId vs string fields
      // Only check string department fields to avoid casting errors
      let existingInLeaders = null;
      try {
        existingInLeaders = await Leader.findOne({
          department: { $type: "string", $eq: name.trim() }
        });
        // Console log removed
      } catch (error) {
        // Console log removed
      }

      if (existingByName || existingInLeaders) {
        return res.status(409).json({
          success: false,
          message: "Tên phòng ban đã tồn tại"
        });
      }
    }

    // Update department in Department model if it exists
    if (existingDepartment) {
      // Console log removed
      existingDepartment.name = name.trim();
      
      // Update level if provided
      if (level !== undefined && level !== null) {
        const parsedLevel = parseInt(level);
        const oldLevel = existingDepartment.level;
        
        // Console log removed
        
        // If level is changing and new level already exists, shift departments down
        if (oldLevel !== parsedLevel) {
          // If moving to a lower level (higher priority), shift departments down
          if (parsedLevel < oldLevel) {
            // Shift all departments from target level down to (old level - 1) down by 1
            await Department.updateMany(
              {
                _id: { $ne: existingDepartment._id },
                level: { $gte: parsedLevel, $lt: oldLevel }
              },
              {
                $inc: { level: 1 }
              }
            );
            // Console log removed
          } 
          // If moving to a higher level (lower priority), shift departments up
          else if (parsedLevel > oldLevel) {
            // Shift all departments from (old level + 1) up to target level up by 1
            await Department.updateMany(
              {
                _id: { $ne: existingDepartment._id },
                level: { $gt: oldLevel, $lte: parsedLevel }
              },
              {
                $inc: { level: -1 }
              }
            );
            // Console log removed
          }
        }
        
        existingDepartment.level = parsedLevel;
      }
      
      const savedDepartment = await existingDepartment.save();
      // Console log removed

      // Leaders that reference this department by ObjectId don't need updating
      // since they reference the department by ID, not by name
      // Console log removed
    }

    // Only update leaders that still use string department names (for backward compatibility)
    // We need to be careful to only update string fields, not ObjectId fields
    // Console log removed
    try {
      const stringUpdateResult = await Leader.updateMany(
        { 
          department: { $type: "string", $eq: oldName }
        },
        { 
          $set: { department: name.trim() }
        }
      );
      // Console log removed
    } catch (stringUpdateError) {
      // Console log removed
    }

    res.status(200).json({
      success: true,
      data: { 
        id, 
        name: name.trim(),
        level: existingDepartment ? existingDepartment.level : (level || 5)
      },
      message: "Cập nhật phòng ban thành công"
    });
  } catch (error) {
    // Console error removed
    res.status(500).json({
      success: false,
      message: "Lỗi server khi cập nhật phòng ban"
    });
  }
};

// Delete department (only if no leaders belong to it)
const deleteDepartment = async (req, res) => {
  try {
    const { id } = req.params;

    // Console log removed

    // Try to find department in Department model first
    const existingDepartment = await Department.findById(id);
    let departmentName = null;

    if (existingDepartment) {
      departmentName = existingDepartment.name;
      // Console log removed
    } else {
      // Console log removed
      return res.status(404).json({
        success: false,
        message: "Không tìm thấy phòng ban"
      });
    }

    // Check if any leaders belong to this department
    // Check both ObjectId references and string references (for backward compatibility)
    let leadersCount = 0;

    try {
      // Count leaders with ObjectId reference to this department (only if id is valid ObjectId)
      let objectIdCount = 0;
      if (id.match(/^[0-9a-fA-F]{24}$/)) {
        objectIdCount = await Leader.countDocuments({ department: id });
        // Console log removed
      } else {
        // Console log removed
      }

      // Count leaders with string reference to this department name
      // Use aggregation to avoid casting issues
      const stringCountResult = await Leader.aggregate([
        {
          $match: {
            department: { $type: "string" },
            department: departmentName
          }
        },
        {
          $count: "count"
        }
      ]);
      const stringCount = stringCountResult.length > 0 ? stringCountResult[0].count : 0;
      // Console log removed

      leadersCount = objectIdCount + stringCount;
      // Console log removed
    } catch (countError) {
      // Console error removed
      // If there's an error counting, assume there might be leaders and prevent deletion
      return res.status(500).json({
        success: false,
        message: "Lỗi khi kiểm tra lãnh đạo thuộc phòng ban"
      });
    }

    if (leadersCount > 0) {
      return res.status(400).json({
        success: false,
        message: `Không thể xóa! Còn ${leadersCount} lãnh đạo thuộc phòng ban này`
      });
    }

    // Check if any positions belong to this department
    try {
      let positionsCount = 0;
      if (id.match(/^[0-9a-fA-F]{24}$/)) {
        positionsCount = await Position.countDocuments({ department: id });
        // Console log removed

        if (positionsCount > 0) {
          // Delete all positions for this department
          await Position.deleteMany({ department: id });
          // Console log removed
        }
      } else {
        // Console log removed
      }
    } catch (positionError) {
      // Console error removed
      // Continue with department deletion even if position cleanup fails
    }

    // Delete from Department model
    await Department.findByIdAndDelete(id);
    // Console log removed

    res.status(200).json({
      success: true,
      message: "Xóa phòng ban thành công"
    });
  } catch (error) {
    // Console error removed
    res.status(500).json({
      success: false,
      message: "Lỗi server khi xóa phòng ban"
    });
  }
};

// Reorder departments by updating their level values
const reorderDepartments = async (req, res) => {
  try {
    const { departmentOrder } = req.body;

    if (!Array.isArray(departmentOrder)) {
      return res.status(400).json({
        success: false,
        message: "departmentOrder phải là một mảng"
      });
    }

    // Validate that all items have required fields
    for (let i = 0; i < departmentOrder.length; i++) {
      const item = departmentOrder[i];
      if (!item._id || typeof item.level !== 'number') {
        return res.status(400).json({
          success: false,
          message: `Phần tử thứ ${i + 1} thiếu _id hoặc level`
        });
      }
    }

    // Update departments in bulk
    const updatePromises = departmentOrder.map(item =>
      Department.findByIdAndUpdate(
        item._id,
        { level: item.level },
        { new: true }
      )
    );

    const updatedDepartments = await Promise.all(updatePromises);

    // Check if any updates failed
    const failedUpdates = updatedDepartments.filter(dept => !dept);
    if (failedUpdates.length > 0) {
      return res.status(400).json({
        success: false,
        message: "Một số phòng ban không thể cập nhật"
      });
    }

    res.status(200).json({
      success: true,
      data: updatedDepartments,
      message: "Cập nhật thứ tự phòng ban thành công"
    });
  } catch (error) {
    // Console error removed
    res.status(500).json({
      success: false,
      message: "Lỗi server khi cập nhật thứ tự phòng ban"
    });
  }
};

// Update department level with automatic shifting
const updateDepartmentLevel = async (req, res) => {
  try {
    const { id } = req.params;
    const { level } = req.body;

    // Console log removed

    if (level === undefined || level === null) {
      return res.status(400).json({
        success: false,
        message: "Level không được để trống"
      });
    }

    const parsedLevel = parseInt(level);
    if (isNaN(parsedLevel) || parsedLevel < 1) {
      return res.status(400).json({
        success: false,
        message: "Level phải là số nguyên dương"
      });
    }

    // Find the department to update
    const existingDepartment = await Department.findById(id);
    if (!existingDepartment) {
      return res.status(404).json({
        success: false,
        message: "Không tìm thấy phòng ban"
      });
    }

    const oldLevel = existingDepartment.level;
    // Console log removed

    // If level is not changing, just return success
    if (oldLevel === parsedLevel) {
      return res.status(200).json({
        success: true,
        data: {
          _id: existingDepartment._id,
          name: existingDepartment.name,
          level: existingDepartment.level
        },
        message: "Level không thay đổi"
      });
    }

    // Find if any other department has the target level and shift accordingly
    if (oldLevel !== parsedLevel) {
      // If moving to a lower level (higher priority), shift departments down
      if (parsedLevel < oldLevel) {
        // Shift all departments from target level down to (old level - 1) down by 1
        await Department.updateMany(
          {
            _id: { $ne: existingDepartment._id },
            level: { $gte: parsedLevel, $lt: oldLevel }
          },
          {
            $inc: { level: 1 }
          }
        );
        // Console log removed
      } 
      // If moving to a higher level (lower priority), shift departments up
      else if (parsedLevel > oldLevel) {
        // Shift all departments from (old level + 1) up to target level up by 1
        await Department.updateMany(
          {
            _id: { $ne: existingDepartment._id },
            level: { $gt: oldLevel, $lte: parsedLevel }
          },
          {
            $inc: { level: -1 }
          }
        );
        // Console log removed
      }
    }

    // Update the target department's level
    existingDepartment.level = parsedLevel;
    await existingDepartment.save();

    // Console log removed

    // Return all departments with updated levels
    const allDepartments = await Department.find({}).sort({ level: 1, createdAt: 1 });

    res.status(200).json({
      success: true,
      data: allDepartments,
      message: "Cập nhật level phòng ban thành công"
    });
  } catch (error) {
    // Console error removed
    res.status(500).json({
      success: false,
      message: "Lỗi server khi cập nhật level phòng ban"
    });
  }
};

module.exports = {
  getAllDepartments,
  createDepartment,
  updateDepartment,
  deleteDepartment,
  reorderDepartments,
  updateDepartmentLevel,
  normalizeDepartmentLevels
};
