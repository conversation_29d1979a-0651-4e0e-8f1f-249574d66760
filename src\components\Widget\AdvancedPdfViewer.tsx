"use client";

import { useState, useRef, useCallback, useEffect } from "react";
import { Document, Page, pdfjs } from "react-pdf";
import { 
  ChevronLeft, 
  ChevronRight, 
  ZoomIn, 
  ZoomOut, 
  Download, 
  Maximize, 
  Minimize, 
  RotateCw, 
  Eye, 
  EyeOff,
  Search,
  BookOpen,
  X
} from "lucide-react";
import { createPortal } from "react-dom";

// Set up PDF.js worker
pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjs.version}/legacy/build/pdf.worker.min.mjs`;

interface AdvancedPdfViewerProps {
  fileUrl: string;
  fileName?: string;
}

function AdvancedPdfViewer({ fileUrl, fileName = "document.pdf" }: AdvancedPdfViewerProps) {
  const [numPages, setNumPages] = useState<number>(0);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [scale, setScale] = useState<number>(1.0);
  const [rotation, setRotation] = useState<number>(0);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string>("");
  const [showAllPages, setShowAllPages] = useState<boolean>(false);
  const [isFullscreen, setIsFullscreen] = useState<boolean>(false);
  const [showThumbnails, setShowThumbnails] = useState<boolean>(false);
  
  const containerRef = useRef<HTMLDivElement>(null);
  const fullscreenRef = useRef<HTMLDivElement>(null);
  const fileCusUrl = "/api/pdf-proxy?url=" + encodeURIComponent(fileUrl);

  const onDocumentLoadSuccess = useCallback(({ numPages }: { numPages: number }) => {
    setNumPages(numPages);
    setIsLoading(false);
    setError("");
  }, []);

  const onDocumentLoadError = useCallback((error: Error) => {
    setError("Không thể tải file PDF. Vui lòng thử lại.");
    setIsLoading(false);
    // PDF load error handled silently
  }, []);

  // Navigation functions
  const nextPage = () => {
    if (currentPage < numPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const prevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const goToPage = (page: number) => {
    if (page >= 1 && page <= numPages) {
      setCurrentPage(page);
    }
  };

  // Zoom functions
  const zoomIn = () => {
    setScale(prevScale => Math.min(prevScale + 0.2, 3.0));
  };

  const zoomOut = () => {
    setScale(prevScale => Math.max(prevScale - 0.2, 0.5));
  };

  const resetZoom = () => {
    setScale(1.0);
  };

  // Rotation
  const rotate = () => {
    setRotation(prevRotation => (prevRotation + 90) % 360);
  };

  // View mode toggles
  const toggleViewMode = () => {
    setShowAllPages(!showAllPages);
    if (!showAllPages) {
      setCurrentPage(1);
    }
  };

  const toggleThumbnails = () => {
    setShowThumbnails(!showThumbnails);
  };

  // Fullscreen handling
  const enterFullscreen = () => {
    setIsFullscreen(true);
    document.body.style.overflow = 'hidden';
  };

  const exitFullscreen = () => {
    setIsFullscreen(false);
    document.body.style.overflow = 'auto';
  };

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (!isFullscreen) return;
      
      switch (e.key) {
        case 'ArrowLeft':
          prevPage();
          break;
        case 'ArrowRight':
          nextPage();
          break;
        case '+':
        case '=':
          zoomIn();
          break;
        case '-':
          zoomOut();
          break;
        case '0':
          resetZoom();
          break;
        case 'r':
        case 'R':
          rotate();
          break;
        case 'Escape':
          exitFullscreen();
          break;
      }
    };

    if (isFullscreen) {
      document.addEventListener('keydown', handleKeyPress);
    }

    return () => {
      document.removeEventListener('keydown', handleKeyPress);
    };
  }, [isFullscreen, currentPage, numPages]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      document.body.style.overflow = 'auto';
    };
  }, []);

  const ToolbarComponent = ({ isCompact = false }) => (
    <div className={`bg-gray-100 border-b px-4 py-3 flex items-center justify-between flex-wrap gap-2 ${isCompact ? 'py-2' : ''}`}>
      <div className="flex items-center space-x-2">
        <button
          onClick={prevPage}
          disabled={currentPage <= 1 || showAllPages}
          className={`p-2 rounded transition-colors ${
            isCompact 
              ? 'bg-gray-700 border border-gray-600 hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed text-white' 
              : 'bg-white border hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed'
          }`}
          title="Trang trước (←)"
        >
          <ChevronLeft className="w-4 h-4" />
        </button>
        
        <div className="flex items-center space-x-2">
          <input
            type="number"
            value={currentPage}
            onChange={(e) => goToPage(parseInt(e.target.value))}
            min={1}
            max={numPages}
            disabled={showAllPages}
            className={`w-16 px-2 py-1 text-center border rounded disabled:opacity-50 ${
              isCompact 
                ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' 
                : 'bg-white border-gray-300'
            }`}
          />
          <span className={`text-sm ${isCompact ? 'text-gray-300' : 'text-gray-600'}`}>/ {numPages}</span>
        </div>

        <button
          onClick={nextPage}
          disabled={currentPage >= numPages || showAllPages}
          className={`p-2 rounded transition-colors ${
            isCompact 
              ? 'bg-gray-700 border border-gray-600 hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed text-white' 
              : 'bg-white border hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed'
          }`}
          title="Trang sau (→)"
        >
          <ChevronRight className="w-4 h-4" />
        </button>
      </div>

      <div className="flex items-center space-x-2">
        <button
          onClick={toggleThumbnails}
          className={`p-2 rounded transition-colors ${
            isCompact 
              ? 'bg-gray-700 border border-gray-600 hover:bg-gray-600 text-white' 
              : 'bg-white border hover:bg-gray-50'
          }`}
          title="Hiển thị thumbnails"
        >
          <BookOpen className="w-4 h-4" />
        </button>

        <button
          onClick={toggleViewMode}
          className={`p-2 rounded transition-colors ${
            isCompact 
              ? 'bg-gray-700 border border-gray-600 hover:bg-gray-600 text-white' 
              : 'bg-white border hover:bg-gray-50'
          }`}
          title={showAllPages ? "Xem từng trang" : "Xem tất cả trang"}
        >
          {showAllPages ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
        </button>

        <button
          onClick={zoomOut}
          className={`p-2 rounded transition-colors ${
            isCompact 
              ? 'bg-gray-700 border border-gray-600 hover:bg-gray-600 text-white' 
              : 'bg-white border hover:bg-gray-50'
          }`}
          title="Thu nhỏ (-)"
        >
          <ZoomOut className="w-4 h-4" />
        </button>
        
        <button
          onClick={resetZoom}
          className={`text-sm min-w-[60px] text-center border rounded px-2 py-1 transition-colors ${
            isCompact 
              ? 'bg-gray-700 border-gray-600 text-white hover:bg-gray-600' 
              : 'bg-white border-gray-300 text-gray-600 hover:bg-gray-50'
          }`}
          title="Reset zoom (0)"
        >
          {Math.round(scale * 100)}%
        </button>
        
        <button
          onClick={zoomIn}
          className={`p-2 rounded transition-colors ${
            isCompact 
              ? 'bg-gray-700 border border-gray-600 hover:bg-gray-600 text-white' 
              : 'bg-white border hover:bg-gray-50'
          }`}
          title="Phóng to (+)"
        >
          <ZoomIn className="w-4 h-4" />
        </button>

        <button
          onClick={rotate}
          className={`p-2 rounded transition-colors ${
            isCompact 
              ? 'bg-gray-700 border border-gray-600 hover:bg-gray-600 text-white' 
              : 'bg-white border hover:bg-gray-50'
          }`}
          title="Xoay (R)"
        >
          <RotateCw className="w-4 h-4" />
        </button>

        {!isFullscreen ? (
          <button
            onClick={enterFullscreen}
            className="p-2 rounded bg-green-600 text-white hover:bg-green-700 transition-colors"
            title="Toàn màn hình"
          >
            <Maximize className="w-4 h-4" />
          </button>
        ) : (
          <button
            onClick={exitFullscreen}
            className="p-2 rounded bg-red-600 text-white hover:bg-red-700 transition-colors"
            title="Thoát toàn màn hình (Esc)"
          >
            <Minimize className="w-4 h-4" />
          </button>
        )}

        <a
          href={fileUrl}
          target="_blank"
          rel="noopener noreferrer"
          className="p-2 rounded bg-blue-600 text-white hover:bg-blue-700 transition-colors"
          title="Tải xuống"
        >
          <Download className="w-4 h-4" />
        </a>
      </div>
    </div>
  );

  const PdfContentComponent = ({ containerHeight = 'auto' }) => (
    <div 
      ref={isFullscreen ? fullscreenRef : containerRef}
      className="bg-gray-200 overflow-auto flex justify-center"
      style={{ 
        minHeight: containerHeight === 'auto' ? '850px' : containerHeight,
        maxHeight: containerHeight === 'auto' ? '1000px' : containerHeight,
        height: containerHeight === '100%' ? '100%' : 'auto'
      }}
    >
      {isLoading && (
        <div className="flex items-center justify-center h-full">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Đang tải PDF...</p>
          </div>
        </div>
      )}

      <div className={`flex ${showThumbnails ? 'flex-row' : 'flex-col'}`}>
        {/* Thumbnails Panel */}
        {showThumbnails && (
          <div className={`bg-gray-100 border-r overflow-y-auto h-full ${
            isFullscreen ? 'w-72' : 'w-64'
          }`}>
            <div className="p-2">
              <h3 className={`text-sm font-semibold mb-2 ${
                isFullscreen ? 'text-gray-800' : 'text-gray-800'
              }`}>Trang</h3>
              <div className="space-y-2">
                <Document file={fileCusUrl} loading="">
                  {numPages > 0 && Array.from({ length: numPages }, (_, index) => (
                    <div 
                      key={index}
                      className={`cursor-pointer border-2 transition-all ${
                        currentPage === index + 1 ? 'border-blue-500' : 'border-gray-300'
                      }`}
                      onClick={() => goToPage(index + 1)}
                    >
                      <Page 
                        pageNumber={index + 1}
                        scale={0.2}
                        renderTextLayer={false}
                        renderAnnotationLayer={false}
                      />
                      <div className="text-xs text-center py-1 bg-gray-50">
                        {index + 1}
                      </div>
                    </div>
                  ))}
                </Document>
              </div>
            </div>
          </div>
        )}

        {/* Main PDF Content */}
        <div className="flex-1 flex flex-col items-center py-4 w-full">
          <Document 
            file={fileCusUrl} 
            onLoadSuccess={onDocumentLoadSuccess}
            onLoadError={onDocumentLoadError}
            loading=""
            className="max-w-full"
          >
            {showAllPages ? (
              // Show all pages
              numPages > 0 && Array.from({ length: numPages }, (_, index) => (
                <div key={index} className="mb-4 shadow-lg max-w-full">
                  <Page 
                    pageNumber={index + 1}
                    scale={scale}
                    rotate={rotation}
                    renderTextLayer={false}
                    renderAnnotationLayer={false}
                    className="mx-auto max-w-full h-auto"
                  />
                </div>
              ))
            ) : (
              // Show single page
              numPages > 0 && (
                <div className="shadow-lg max-w-full">
                  <Page 
                    pageNumber={currentPage}
                    scale={scale}
                    rotate={rotation}
                    renderTextLayer={false}
                    renderAnnotationLayer={false}
                    className="mx-auto max-w-full h-auto"
                  />
                </div>
              )
            )}
          </Document>
        </div>
      </div>
    </div>
  );

  if (error) {
    return (
      <div className="flex items-center justify-center p-8 border-2 border-dashed border-gray-300 rounded-lg bg-gray-50">
        <div className="text-center">
          <div className="text-red-500 mb-2">
            <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <p className="text-gray-600">{error}</p>
          <a 
            href={fileUrl} 
            target="_blank" 
            rel="noopener noreferrer"
            className="inline-flex items-center mt-3 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
          >
            <Download className="w-4 h-4 mr-2" />
            Tải xuống PDF
          </a>
        </div>
      </div>
    );
  }

  const regularViewer = (
    <div className="w-full border rounded-lg shadow-lg bg-white overflow-hidden">
      <ToolbarComponent />
      <PdfContentComponent />
      
      {/* Status Bar */}
      <div className="bg-gray-50 border-t px-4 py-2 text-sm text-gray-600 flex justify-between items-center">
        <span>
          {showAllPages ? `Hiển thị tất cả ${numPages} trang` : `Trang ${currentPage} / ${numPages}`}
        </span>
        <span>Tỷ lệ: {Math.round(scale * 100)}% | {fileName}</span>
      </div>
    </div>
  );

  const fullscreenViewer = (
    <div className="fixed inset-0 z-50 bg-black flex flex-col">
      <div className="bg-gray-900 text-white">
        <div className="flex items-center justify-between px-4 py-2">
          <h3 className="text-lg font-semibold">{fileName}</h3>
          <button
            onClick={exitFullscreen}
            className="p-2 hover:bg-gray-700 rounded transition-colors"
            title="Đóng (Esc)"
          >
            <X className="w-5 h-5" />
          </button>
        </div>
        <ToolbarComponent isCompact />
      </div>
      
      <div className="flex-1 overflow-hidden">
        <PdfContentComponent containerHeight="100%" />
      </div>

      {/* Fullscreen Status Bar */}
      <div className="bg-gray-900 text-white px-4 py-2 text-sm flex justify-between items-center">
        <span>
          {showAllPages ? `Hiển thị tất cả ${numPages} trang` : `Trang ${currentPage} / ${numPages}`}
        </span>
        <span>Tỷ lệ: {Math.round(scale * 100)}% | Nhấn Esc để thoát | Phím tắt: ← → (trang), +/- (zoom), R (xoay)</span>
      </div>
    </div>
  );

  return (
    <>
      {regularViewer}
      {isFullscreen && typeof document !== 'undefined' && createPortal(fullscreenViewer, document.body)}
    </>
  );
}

export default AdvancedPdfViewer;
