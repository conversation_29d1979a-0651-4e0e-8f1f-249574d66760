"use client";

import React, { useState, useEffect } from 'react';
import { useAppContext } from "@/app/app-provider";
import analyticsApiRequest, { AnalyticsOverview, TopPost, ChartDataPoint } from "@/apiRequests/analytics";
import { toast } from "react-toastify";
import { Eye, TrendingUp, Users, Home, FileText, Calendar } from "react-feather";

const AnalyticsPage = () => {
  const { sessionToken } = useAppContext();
  const [loading, setLoading] = useState(true);
  const [overview, setOverview] = useState<AnalyticsOverview | null>(null);
  const [topPosts, setTopPosts] = useState<TopPost[]>([]);
  const [chartData, setChartData] = useState<ChartDataPoint[]>([]);
  const [selectedPeriod, setSelectedPeriod] = useState(7);

  useEffect(() => {
    console.log("Analytics page mounted, sessionToken:", !!sessionToken);
    // Fetch analytics data regardless of sessionToken since our API doesn't require auth
    fetchAnalytics();
  }, [selectedPeriod]);

  const fetchAnalytics = async () => {
    try {
      console.log("Fetching analytics data...");
      setLoading(true);

      const [overviewResult, chartResult] = await Promise.allSettled([
        analyticsApiRequest.getOverview(sessionToken || ""),
        analyticsApiRequest.getChartData(sessionToken || "", selectedPeriod)
      ]);

      console.log("Overview result:", overviewResult);
      console.log("Chart result:", chartResult);

      if (overviewResult.status === 'fulfilled') {
        const response = overviewResult.value;
        console.log("Full overview response:", response);

        // Check if it's a direct API response or wrapped in payload
        if (response.payload && response.payload.success) {
          const data = response.payload.analytics;
          setOverview(data.overview);
          setTopPosts(data.topPostsToday);
          console.log("Overview data set (payload):", data.overview);
        } else if (response.success) {
          // Direct API response
          const data = response.analytics;
          setOverview(data.overview);
          setTopPosts(data.topPostsToday);
          console.log("Overview data set (direct):", data.overview);
        } else {
          console.error("Overview fetch failed - no success flag");
        }
      } else {
        console.error("Overview fetch failed:", overviewResult);
      }

      if (chartResult.status === 'fulfilled') {
        const response = chartResult.value;
        console.log("Full chart response:", response);

        if (response.payload && response.payload.success) {
          setChartData(response.payload.chartData);
          console.log("Chart data set (payload):", response.payload.chartData);
        } else if (response.success) {
          setChartData(response.chartData);
          console.log("Chart data set (direct):", response.chartData);
        } else {
          console.error("Chart fetch failed - no success flag");
        }
      } else {
        console.error("Chart fetch failed:", chartResult);
      }

      toast.success("Đã tải thống kê thành công");
    } catch (error) {
      console.error("Analytics fetch error:", error);

      // Set fallback data on error
      setOverview({
        totalViewsAllTime: 0,
        totalViewsThisMonth: 0,
        totalViewsThisWeek: 0,
        totalViewsToday: 0,
        homeViewsToday: 0,
        postViewsToday: 0,
        uniqueVisitorsToday: 0,
        homeViewsGrowth: 0,
        postViewsGrowth: 0
      });
      setTopPosts([]);
      setChartData([]);

      toast.error("Có lỗi xảy ra khi tải thống kê - hiển thị dữ liệu mặc định");
    } finally {
      setLoading(false);
    }
  };

  const StatCard = ({ title, value, icon: Icon, growth, color = "blue" }: {
    title: string;
    value: number;
    icon: any;
    growth?: number;
    color?: string;
  }) => (
    <div className="bg-white rounded-lg shadow-md p-6 border-l-4" style={{ borderLeftColor: color === "blue" ? "#3b82f6" : color === "green" ? "#10b981" : "#ef4444" }}>
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{value.toLocaleString()}</p>
          {growth !== undefined && (
            <p className={`text-sm ${growth >= 0 ? 'text-green-600' : 'text-red-600'} flex items-center mt-1`}>
              <TrendingUp className="w-4 h-4 mr-1" />
              {growth >= 0 ? '+' : ''}{growth.toFixed(1)}%
            </p>
          )}
        </div>
        <Icon className="w-8 h-8 text-gray-400" />
      </div>
    </div>
  );



  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {[1, 2, 3, 4].map(i => (
              <div key={i} className="bg-gray-200 h-32 rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">Thống kê truy cập</h1>
        <div className="flex space-x-2">
          {[7, 30, 90].map(days => (
            <button
              key={days}
              onClick={() => setSelectedPeriod(days)}
              className={`px-4 py-2 rounded-lg text-sm font-medium ${
                selectedPeriod === days
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              {days} ngày
            </button>
          ))}
        </div>
      </div>

      {/* Overview Stats */}
      {overview && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <StatCard
            title="Tổng lượt xem hôm nay"
            value={overview.totalViewsToday}
            icon={Eye}
            color="blue"
          />
          <StatCard
            title="Lượt xem trang chủ"
            value={overview.homeViewsToday}
            icon={Home}
            growth={overview.homeViewsGrowth}
            color="green"
          />
          <StatCard
            title="Lượt xem bài viết"
            value={overview.postViewsToday}
            icon={FileText}
            growth={overview.postViewsGrowth}
            color="red"
          />
          <StatCard
            title="Người dùng duy nhất"
            value={overview.uniqueVisitorsToday}
            icon={Users}
            color="purple"
          />
        </div>
      )}

      {/* Chart Data */}
      {chartData.length > 0 && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-lg font-semibold mb-4">Biểu đồ lượt xem ({selectedPeriod} ngày qua)</h2>
          <div className="space-y-2">
            {chartData.map((day, index) => (
              <div key={index} className="flex items-center justify-between py-2 border-b">
                <span className="text-sm text-gray-600">{day.date}</span>
                <div className="flex space-x-4 text-sm">
                  <span className="text-blue-600">Trang chủ: {day.homeViews}</span>
                  <span className="text-green-600">Bài viết: {day.postViews}</span>
                  <span className="text-purple-600">Tổng: {day.totalViews}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Top Posts */}
      {topPosts.length > 0 && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-lg font-semibold mb-4">Bài viết được xem nhiều nhất hôm nay</h2>
          <div className="space-y-3">
            {topPosts.map((post, index) => (
              <div key={post.postId} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <span className="flex items-center justify-center w-6 h-6 bg-blue-600 text-white text-xs font-bold rounded-full">
                    {index + 1}
                  </span>
                  <div>
                    <h3 className="font-medium text-gray-900">{post.title}</h3>
                    <p className="text-sm text-gray-500">/{post.slug}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <Eye className="w-4 h-4" />
                  <span>{post.views} lượt xem</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Summary Stats */}
      {overview && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold mb-2">Tuần này</h3>
            <p className="text-2xl font-bold text-blue-600">{overview.totalViewsThisWeek.toLocaleString()}</p>
            <p className="text-sm text-gray-600">lượt xem</p>
          </div>
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold mb-2">Tháng này</h3>
            <p className="text-2xl font-bold text-green-600">{overview.totalViewsThisMonth.toLocaleString()}</p>
            <p className="text-sm text-gray-600">lượt xem</p>
          </div>
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold mb-2">Tổng cộng</h3>
            <p className="text-2xl font-bold text-purple-600">{overview.totalViewsAllTime.toLocaleString()}</p>
            <p className="text-sm text-gray-600">lượt xem</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default AnalyticsPage;
