"use client";
import React from "react";
import RenderImage from "@/components/Widget/renderImage";
import Link from "next/link";
import CategoryTitle from "@/components/Widget/CategoryTitle";

type Blog = {
  _id: string;
  slug: string;
  title: string;
  short?: string;
  featureImg?: { path: string };
  categories?: any[]; // Update this if you have a specific shape for categories
};

type Category = {
  slug: string;
  name: string;
};

interface NewsFourProps {
  blogs: Blog[];
  category?: Category; // Optional
}

const NewsFour: React.FC<NewsFourProps> = ({ blogs, category }) => {
  return (
    <div className="h-full">
      {category?.slug && (
        <div className="w-full mb-6">
          <h2 className="text-lg sm:text-xl uppercase font-semibold text-white py-3 px-4 w-full rounded-lg shadow-md transition-all duration-300 hover:shadow-lg"
              style={{
                backgroundColor: '#e4393c',
                background: 'linear-gradient(135deg, #e4393c 0%, #d32f2f 100%)'
              }}>
            <span className="flex items-center">
              <span className="w-1 h-5 bg-white rounded-full mr-3"></span>
              {category.name}
            </span>
          </h2>
        </div>
      )}

      <div className="space-y-4">
        {blogs?.slice(0, 6).map((item, index) => {
          const isFirstItem = index === 0;

          return (
            <article
              key={item._id}
              className={`group transition-all duration-300 hover:transform hover:scale-[1.02] ${
                isFirstItem ? 'pb-4 border-b-2 border-gray-200 dark:border-gray-700' : 'pb-3 border-b border-gray-100 dark:border-gray-800'
              }`}
            >
              {isFirstItem && (
                <div className="single-post-thumbnail mb-3 overflow-hidden rounded-lg shadow-md group-hover:shadow-lg transition-all duration-300">
                  <Link href={`/blog/${item.slug}`}>
                    <div className="relative overflow-hidden rounded-lg">
                      <RenderImage
                        img_url={item.featureImg?.path}
                        title={item.title}
                        categories={item.categories || []}
                      />
                      {/* Overlay effect */}
                      <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                      {/* Category badge */}
                      {item.categories && item.categories.length > 0 && (
                        <div className="absolute top-2 left-2 bg-red-600 text-white px-2 py-1 rounded text-xs font-semibold shadow-lg">
                          {item.categories[0].name}
                        </div>
                      )}
                    </div>
                  </Link>
                </div>
              )}

              <div className="space-y-2">
                <Link
                  href={`/blog/${item.slug}`}
                  className={`block ${isFirstItem ? 'text-lg' : 'text-md'} font-semibold line-clamp-2 hover:text-red-600 transition-colors duration-300 leading-tight`}
                >
                  {item.title}
                </Link>

                {item.short && (
                  <p className={`text-gray-600 dark:text-gray-300 ${isFirstItem ? 'line-clamp-3' : 'line-clamp-2'} text-sm leading-relaxed`}>
                    {item.short}
                  </p>
                )}

                {/* Meta info for first item */}
                {isFirstItem && (
                  <div className="flex items-center text-xs text-gray-500 dark:text-gray-400 space-x-3 mt-2">
                    <span className="flex items-center">
                      <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                      </svg>
                      3 phút đọc
                    </span>
                    <span>•</span>
                    <span>Hôm nay</span>
                  </div>
                )}
              </div>
            </article>
          );
        })}

        {/* View more link */}
        {category?.slug && (
          <div className="pt-4">
            <Link
              href={`/${category.slug}`}
              className="inline-flex items-center text-red-600 hover:text-red-700 font-medium text-sm transition-colors duration-300 group"
            >
              Xem thêm tin tức
              <svg className="w-4 h-4 ml-1 transition-transform duration-300 group-hover:translate-x-1" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </Link>
          </div>
        )}
      </div>
    </div>
  );
};

export default NewsFour;
