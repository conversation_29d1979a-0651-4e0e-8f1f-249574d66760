"use client";

import { useEffect, useRef } from "react";
import dynamic from "next/dynamic";
import "plyr/dist/plyr.css";
import { getFirstVideoUrl, isValidVideoData } from "@/utils/videoUtils";

export default function BlogContent({
  html,
  video,
}: {
  html: string;
  video?: any; // Changed from string to any to handle various video data formats
}) {
  const contentRef = useRef<HTMLDivElement>(null);
  const playerRef = useRef<HTMLVideoElement>(null);

  // Get the actual video URL from the video data
  const videoUrl = getFirstVideoUrl(video);
  const hasValidVideo = isValidVideoData(video);

  useEffect(() => {
    // Only initialize Plyr if there are actual videos to play
    if (!hasValidVideo && !contentRef.current?.querySelectorAll("video[src]").length) {
      return;
    }

    // Dynamic import Plyr to avoid SSR issues
    const initPlyr = async () => {
      const Plyr = (await import("plyr")).default;

      // Init Plyr for video above the content (only if we have a valid video URL)
      if (playerRef.current && videoUrl && !playerRef.current.classList.contains("plyr-initialized")) {
        const player = playerRef.current;

        player.classList.add("plyr-initialized");
        player.setAttribute("playsinline", "true");
        player.removeAttribute("controls");

        new Plyr(player, {
          controls: [
            "play",
            "play-large",
            "progress",
            "current-time",
            "mute",
            "volume",
            "fullscreen",
          ],
          autoplay: false,
          clickToPlay: true,
        });
      }

      // Init Plyr for videos inside html content (only for videos with valid src)
      if (contentRef.current) {
        const embeddedVideos = contentRef.current.querySelectorAll("video[src]:not([src=''])");
        embeddedVideos.forEach((videoEl) => {
          if (videoEl.classList.contains("plyr-initialized")) return;

          // Skip if this is a preview video or has no valid src
          if (videoEl.classList.contains('blog-video-preview') ||
              videoEl.getAttribute('data-video-type') === 'preview' ||
              !videoEl.getAttribute('src')?.trim()) {
            return;
          }

          videoEl.classList.add("plyr-initialized");
          videoEl.setAttribute("playsinline", "true");
          videoEl.removeAttribute("controls");

          new Plyr(videoEl, {
            controls: [
              "play",
              "play-large",
              "progress",
              "current-time",
              "mute",
              "volume",
              "fullscreen",
            ],
            autoplay: false,
            clickToPlay: true,
          });
        });
      }
    };

    initPlyr();
  }, [html, video, videoUrl, hasValidVideo]);

  return (
    <div ref={contentRef}>
      {hasValidVideo && videoUrl && (
        <div className="mb-4">
          <video
            ref={playerRef}
            src={videoUrl}
            className="video-player"
            style={{ width: "100%", overflow: "hidden" }}
            playsInline
          >
            <source src={videoUrl} type="video/mp4" />
          </video>
        </div>
      )}

      <div dangerouslySetInnerHTML={{ __html: html }} />
    </div>
  );
}
