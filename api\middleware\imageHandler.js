const fs = require('fs');
const path = require('path');

// Middleware to handle missing images gracefully
const handleMissingImage = (req, res, next) => {
  // Check if this is a request for an image in uploads/media
  if (req.url.startsWith('/uploads/media/')) {
    const imagePath = path.join(__dirname, '..', '..', req.url);

    // Check if file exists
    if (!fs.existsSync(imagePath)) {
      // Console log removed

      // Try to serve placeholder image instead
      const placeholderPath = path.join(__dirname, '..', '..', 'uploads', 'media', 'placeholder.jpg');

      if (fs.existsSync(placeholderPath)) {
        // Serve placeholder image
        res.setHeader('Content-Type', 'image/jpeg');
        res.setHeader('Cache-Control', 'public, max-age=3600'); // Cache for 1 hour
        return res.sendFile(placeholderPath);
      } else {
        // If placeholder doesn't exist, return 404
        res.status(404).json({
          success: false,
          message: 'Image not found',
          url: req.url
        });
        return;
      }
    }
  }

  next();
};

// Function to create a placeholder image if needed
const createPlaceholderImage = (width = 400, height = 300) => {
  // This would create a simple placeholder image
  // For now, we'll just return a path to a default image
  return '/uploads/media/placeholder.jpg';
};

// Function to check and clean up missing image references in posts
const cleanupMissingImages = async (Post) => {
  try {
    const posts = await Post.find({});
    let updatedCount = 0;
    
    for (const post of posts) {
      let needsUpdate = false;
      
      // Check feature image
      if (post.featureImg && post.featureImg.path) {
        const imagePath = path.join(__dirname, '..', '..', post.featureImg.path);
        if (!fs.existsSync(imagePath)) {
          // Console log removed
          // Set to placeholder instead of null since featureImg is required
          post.featureImg = {
            path: 'uploads/media/placeholder.jpg',
            filename: 'placeholder.jpg',
            originalname: 'placeholder.jpg',
            mimetype: 'image/jpeg',
            size: 0
          };
          needsUpdate = true;
        }
      }
      
      // Check images in description
      if (post.desc) {
        const imageRegex = /<img[^>]+src="([^">]+)"/g;
        let match;
        let updatedDesc = post.desc;
        
        while ((match = imageRegex.exec(post.desc)) !== null) {
          const imgUrl = match[1];
          if (imgUrl.includes('/uploads/')) {
            const imagePath = path.join(__dirname, '..', '..', imgUrl);
            if (!fs.existsSync(imagePath)) {
              // Console log removed
              // Remove the broken image tag
              updatedDesc = updatedDesc.replace(match[0], '');
              needsUpdate = true;
            }
          }
        }
        
        if (updatedDesc !== post.desc) {
          post.desc = updatedDesc;
        }
      }
      
      // Check file attachments
      if (post.file && Array.isArray(post.file)) {
        const validFiles = post.file.filter(file => {
          if (file.path) {
            const filePath = path.join(__dirname, '..', '..', file.path);
            const exists = fs.existsSync(filePath);
            if (!exists) {
              // Console log removed
            }
            return exists;
          }
          return false;
        });
        
        if (validFiles.length !== post.file.length) {
          post.file = validFiles;
          needsUpdate = true;
        }
      }
      
      if (needsUpdate) {
        await post.save();
        updatedCount++;
      }
    }
    
    // Console log removed
    return updatedCount;
  } catch (error) {
    // Console error removed
    return 0;
  }
};

// Function to validate image URLs before saving
const validateImageUrls = (imageUrls) => {
  if (!Array.isArray(imageUrls)) {
    imageUrls = [imageUrls];
  }
  
  return imageUrls.filter(url => {
    if (!url) return false;
    
    try {
      const imagePath = path.join(__dirname, '..', '..', url);
      return fs.existsSync(imagePath);
    } catch (error) {
      // Console error removed
      return false;
    }
  });
};

// Function to get image info safely
const getImageInfo = (imagePath) => {
  try {
    const fullPath = path.join(__dirname, '..', '..', imagePath);
    if (fs.existsSync(fullPath)) {
      const stats = fs.statSync(fullPath);
      return {
        exists: true,
        size: stats.size,
        modified: stats.mtime,
        path: imagePath
      };
    }
  } catch (error) {
    // Console error removed
  }
  
  return {
    exists: false,
    path: imagePath
  };
};

module.exports = {
  handleMissingImage,
  createPlaceholderImage,
  cleanupMissingImages,
  validateImageUrls,
  getImageInfo
};
