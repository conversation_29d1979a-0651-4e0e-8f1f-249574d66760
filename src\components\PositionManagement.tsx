"use client";

import { useState, useEffect } from "react";
import { toast } from "react-toastify";
import {
  Plus,
  Edit3,
  Trash2,
  Save,
  X,
  Settings,
  Users,
  ChevronDown,
  ChevronUp
} from "react-feather";
import positionApiRequest, { Position, CreatePositionRequest, UpdatePositionRequest } from "@/apiRequests/position";

interface PositionManagementProps {
  departmentId: string;
  departmentName: string;
  sessionToken: string;
}

export default function PositionManagement({ departmentId, departmentName, sessionToken }: PositionManagementProps) {
  const [positions, setPositions] = useState<Position[]>([]);
  const [loading, setLoading] = useState(false);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [isExpanded, setIsExpanded] = useState(false);
  
  // Form states
  const [newPosition, setNewPosition] = useState<CreatePositionRequest>({
    name: "",
    description: "",
    departmentId: departmentId,
    level: 1,
    order: 0
  });
  const [editPosition, setEditPosition] = useState<UpdatePositionRequest>({});

  // Fetch positions for this department
  const fetchPositions = async () => {
    if (!sessionToken || !departmentId) return;

    try {
      setLoading(true);
      const result = await positionApiRequest.getPositionsByDepartment(departmentId, sessionToken);
      
      if (result.payload.success) {
        setPositions(result.payload.data || []);
      } else {
        toast.error("Không thể tải danh sách chức vụ");
      }
    } catch (error) {
      // Console error removed
      toast.error("Lỗi khi tải danh sách chức vụ");
    } finally {
      setLoading(false);
    }
  };

  // Load positions when component mounts or when expanded
  useEffect(() => {
    if (isExpanded && sessionToken && departmentId) {
      fetchPositions();
    }
  }, [isExpanded, sessionToken, departmentId]);

  // Create new position
  const handleCreatePosition = async () => {
    if (!newPosition.name.trim()) {
      toast.error("Tên chức vụ không được để trống");
      return;
    }

    try {
      const result = await positionApiRequest.createPosition(newPosition, sessionToken);
      
      if (result.payload.success) {
        toast.success("Tạo chức vụ thành công");
        setPositions([...positions, result.payload.data]);
        setShowAddForm(false);
        setNewPosition({
          name: "",
          description: "",
          departmentId: departmentId,
          level: 1,
          order: 0
        });
      } else {
        toast.error(result.payload.message || "Không thể tạo chức vụ");
      }
    } catch (error) {
      // Console error removed
      toast.error("Lỗi khi tạo chức vụ");
    }
  };

  // Update position
  const handleUpdatePosition = async (id: string) => {
    try {
      const result = await positionApiRequest.updatePosition(id, editPosition, sessionToken);
      
      if (result.payload.success) {
        toast.success("Cập nhật chức vụ thành công");
        setPositions(positions.map(pos => 
          pos._id === id ? result.payload.data : pos
        ));
        setEditingId(null);
        setEditPosition({});
      } else {
        toast.error(result.payload.message || "Không thể cập nhật chức vụ");
      }
    } catch (error) {
      // Console error removed
      toast.error("Lỗi khi cập nhật chức vụ");
    }
  };

  // Delete position
  const handleDeletePosition = async (id: string, name: string) => {
    if (!confirm(`Bạn có chắc chắn muốn xóa chức vụ "${name}"?`)) {
      return;
    }

    try {
      const result = await positionApiRequest.deletePosition(id, sessionToken);
      
      if (result.payload.success) {
        toast.success("Xóa chức vụ thành công");
        setPositions(positions.filter(pos => pos._id !== id));
      } else {
        toast.error(result.payload.message || "Không thể xóa chức vụ");
      }
    } catch (error) {
      // Console error removed
      toast.error("Lỗi khi xóa chức vụ");
    }
  };

  // Start editing
  const startEdit = (position: Position) => {
    setEditingId(position._id);
    setEditPosition({
      name: position.name,
      description: position.description,
      level: position.level,
      order: position.order
    });
  };

  // Cancel editing
  const cancelEdit = () => {
    setEditingId(null);
    setEditPosition({});
  };

  return (
    <div className="border-t border-gray-200 bg-gray-50">
      {/* Toggle Header */}
      <div 
        className="px-6 py-4 flex items-center justify-between cursor-pointer hover:bg-gray-100 transition-colors"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center space-x-3">
          <Settings size={20} className="text-gray-600" />
          <span className="font-medium text-gray-700">Quản lý chức vụ</span>
          <span className="text-sm text-gray-500">({positions.length} chức vụ)</span>
        </div>
        {isExpanded ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
      </div>

      {/* Expanded Content */}
      {isExpanded && (
        <div className="px-6 pb-6">
          {/* Add Position Button */}
          <div className="mb-4">
            <button
              onClick={() => setShowAddForm(true)}
              className="flex items-center space-x-2 bg-blue-600 text-white px-3 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm"
            >
              <Plus size={16} />
              <span>Thêm chức vụ</span>
            </button>
          </div>

          {/* Add Form */}
          {showAddForm && (
            <div className="bg-white p-4 rounded-lg border mb-4">
              <h4 className="font-medium mb-3">Thêm chức vụ mới cho {departmentName}</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <input
                  type="text"
                  value={newPosition.name}
                  onChange={(e) => setNewPosition({...newPosition, name: e.target.value})}
                  placeholder="Tên chức vụ"
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                />
                <input
                  type="text"
                  value={newPosition.description || ""}
                  onChange={(e) => setNewPosition({...newPosition, description: e.target.value})}
                  placeholder="Mô tả (tùy chọn)"
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                />
                <input
                  type="number"
                  value={newPosition.level}
                  onChange={(e) => setNewPosition({...newPosition, level: parseInt(e.target.value) || 1})}
                  placeholder="Cấp độ (1-10)"
                  min="1"
                  max="10"
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                />

              </div>
              <div className="flex items-center space-x-2 mt-4">
                <button
                  onClick={handleCreatePosition}
                  disabled={!newPosition.name.trim()}
                  className="flex items-center space-x-2 bg-green-600 text-white px-3 py-2 rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm"
                >
                  <Save size={14} />
                  <span>Lưu</span>
                </button>
                <button
                  onClick={() => {
                    setShowAddForm(false);
                    setNewPosition({
                      name: "",
                      description: "",
                      departmentId: departmentId,
                      level: 1,
                      order: 0
                    });
                  }}
                  className="flex items-center space-x-2 bg-gray-500 text-white px-3 py-2 rounded-lg hover:bg-gray-600 transition-colors text-sm"
                >
                  <X size={14} />
                  <span>Hủy</span>
                </button>
              </div>
            </div>
          )}

          {/* Positions List */}
          {loading ? (
            <div className="text-center py-4">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
              <p className="text-sm text-gray-500 mt-2">Đang tải...</p>
            </div>
          ) : positions.length === 0 ? (
            <div className="text-center py-4">
              <Users className="h-8 w-8 text-gray-400 mx-auto mb-2" />
              <p className="text-sm text-gray-500">Chưa có chức vụ nào</p>
            </div>
          ) : (
            <div className="space-y-2">
              {positions.map((position) => (
                <div key={position._id} className="bg-white p-3 rounded-lg border flex items-center justify-between">
                  <div className="flex-1">
                    {editingId === position._id ? (
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
                        <input
                          type="text"
                          value={editPosition.name || ""}
                          onChange={(e) => setEditPosition({...editPosition, name: e.target.value})}
                          className="px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                          placeholder="Tên chức vụ"
                        />
                        <input
                          type="text"
                          value={editPosition.description || ""}
                          onChange={(e) => setEditPosition({...editPosition, description: e.target.value})}
                          className="px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                          placeholder="Mô tả"
                        />
                        <div className="flex space-x-2">
                          <input
                            type="number"
                            value={editPosition.level || 1}
                            onChange={(e) => setEditPosition({...editPosition, level: parseInt(e.target.value) || 1})}
                            className="px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 w-20"
                            placeholder="Cấp"
                            min="1"
                            max="10"
                          />
                          <input
                            type="number"
                            value={editPosition.order || 0}
                            onChange={(e) => setEditPosition({...editPosition, order: parseInt(e.target.value) || 0})}
                            className="px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 w-20"
                            placeholder="Thứ tự"
                            min="0"
                          />
                        </div>
                      </div>
                    ) : (
                      <div>
                        <div className="flex items-center space-x-3">
                          <h5 className="font-medium text-gray-900 text-sm">{position.name}</h5>
                          {position.isDefault && (
                            <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">Mặc định</span>
                          )}
                          <span className="text-xs text-gray-500">Cấp {position.level}</span>
                          <span className="text-xs text-gray-500">Thứ tự {position.order}</span>
                        </div>
                        {position.description && (
                          <p className="text-xs text-gray-600 mt-1">{position.description}</p>
                        )}
                      </div>
                    )}
                  </div>
                  
                  <div className="flex items-center space-x-1 ml-4">
                    {editingId === position._id ? (
                      <>
                        <button
                          onClick={() => handleUpdatePosition(position._id)}
                          className="p-1 text-green-600 hover:bg-green-100 rounded transition-colors"
                          title="Lưu"
                        >
                          <Save size={14} />
                        </button>
                        <button
                          onClick={cancelEdit}
                          className="p-1 text-gray-600 hover:bg-gray-100 rounded transition-colors"
                          title="Hủy"
                        >
                          <X size={14} />
                        </button>
                      </>
                    ) : (
                      <>
                        <button
                          onClick={() => startEdit(position)}
                          className="p-1 text-blue-600 hover:bg-blue-100 rounded transition-colors"
                          title="Chỉnh sửa"
                        >
                          <Edit3 size={14} />
                        </button>
                        {!position.isDefault && (
                          <button
                            onClick={() => handleDeletePosition(position._id, position.name)}
                            className="p-1 text-red-600 hover:bg-red-100 rounded transition-colors"
                            title="Xóa"
                          >
                            <Trash2 size={14} />
                          </button>
                        )}
                      </>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
}
