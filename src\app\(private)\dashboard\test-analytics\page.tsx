"use client";

import React from 'react';

const TestAnalyticsPage = () => {
  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold text-gray-900 mb-4">Test Analytics Page</h1>
      <p className="text-gray-600">This is a test page to verify routing works.</p>
      
      <div className="mt-6 space-y-4">
        <div className="bg-white p-4 rounded-lg shadow">
          <h2 className="text-lg font-semibold">Test API Call</h2>
          <button 
            onClick={async () => {
              try {
                const response = await fetch('/api/analytics/test');
                const data = await response.json();
                console.log('Test API response:', data);
                alert('Check console for API response');
              } catch (error) {
                console.error('API test error:', error);
                alert('API test failed - check console');
              }
            }}
            className="mt-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Test Analytics API
          </button>
        </div>
        
        <div className="bg-white p-4 rounded-lg shadow">
          <h2 className="text-lg font-semibold">Test Overview API</h2>
          <button 
            onClick={async () => {
              try {
                const response = await fetch('/api/analytics/overview');
                const data = await response.json();
                console.log('Overview API response:', data);
                alert('Check console for Overview API response');
              } catch (error) {
                console.error('Overview API test error:', error);
                alert('Overview API test failed - check console');
              }
            }}
            className="mt-2 px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
          >
            Test Overview API
          </button>
        </div>
      </div>
    </div>
  );
};

export default TestAnalyticsPage;
