"use client";

import React from 'react';

interface LoadingSkeletonProps {
  type?: 'news' | 'card' | 'list' | 'hero';
  count?: number;
  className?: string;
}

const LoadingSkeleton: React.FC<LoadingSkeletonProps> = ({ 
  type = 'news', 
  count = 1, 
  className = '' 
}) => {
  const renderSkeleton = () => {
    switch (type) {
      case 'hero':
        return (
          <div className="animate-pulse">
            <div className="bg-gray-300 dark:bg-gray-700 rounded-lg h-64 md:h-80 mb-4"></div>
            <div className="space-y-3">
              <div className="bg-gray-300 dark:bg-gray-700 rounded h-6 w-3/4"></div>
              <div className="bg-gray-300 dark:bg-gray-700 rounded h-4 w-full"></div>
              <div className="bg-gray-300 dark:bg-gray-700 rounded h-4 w-2/3"></div>
            </div>
          </div>
        );
      
      case 'card':
        return (
          <div className="animate-pulse">
            <div className="bg-gray-300 dark:bg-gray-700 rounded-lg h-48 mb-3"></div>
            <div className="space-y-2">
              <div className="bg-gray-300 dark:bg-gray-700 rounded h-4 w-full"></div>
              <div className="bg-gray-300 dark:bg-gray-700 rounded h-4 w-3/4"></div>
              <div className="bg-gray-300 dark:bg-gray-700 rounded h-3 w-1/2"></div>
            </div>
          </div>
        );
      
      case 'list':
        return (
          <div className="animate-pulse flex space-x-3 py-3">
            <div className="bg-gray-300 dark:bg-gray-700 rounded-lg h-16 w-20 flex-shrink-0"></div>
            <div className="flex-1 space-y-2">
              <div className="bg-gray-300 dark:bg-gray-700 rounded h-4 w-full"></div>
              <div className="bg-gray-300 dark:bg-gray-700 rounded h-3 w-2/3"></div>
            </div>
          </div>
        );
      
      default: // news
        return (
          <div className="animate-pulse">
            <div className="bg-gray-300 dark:bg-gray-700 rounded-lg h-40 mb-3"></div>
            <div className="space-y-2">
              <div className="bg-gray-300 dark:bg-gray-700 rounded h-4 w-full"></div>
              <div className="bg-gray-300 dark:bg-gray-700 rounded h-4 w-4/5"></div>
              <div className="bg-gray-300 dark:bg-gray-700 rounded h-3 w-3/5"></div>
            </div>
          </div>
        );
    }
  };

  return (
    <div className={className}>
      {Array.from({ length: count }, (_, index) => (
        <div key={index} className="mb-4">
          {renderSkeleton()}
        </div>
      ))}
    </div>
  );
};

// Specific skeleton components for different sections
export const NewsOneSkeleton: React.FC = () => (
  <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-3 gap-4 sm:gap-6">
    {/* Main story skeleton */}
    <div className="col-span-1 md:col-span-3 grid grid-cols-1 md:grid-cols-5 gap-4 md:gap-6">
      <div className="col-span-3 animate-pulse">
        <div className="bg-gray-300 dark:bg-gray-700 rounded-lg h-64 mb-3"></div>
      </div>
      <div className="col-span-2 space-y-3 animate-pulse">
        <div className="bg-gray-300 dark:bg-gray-700 rounded h-6 w-full"></div>
        <div className="bg-gray-300 dark:bg-gray-700 rounded h-4 w-full"></div>
        <div className="bg-gray-300 dark:bg-gray-700 rounded h-4 w-3/4"></div>
        <div className="bg-gray-300 dark:bg-gray-700 rounded h-3 w-1/2"></div>
      </div>
    </div>
    
    {/* Secondary stories skeleton */}
    {Array.from({ length: 3 }, (_, index) => (
      <div key={index} className="animate-pulse">
        <div className="bg-gray-300 dark:bg-gray-700 rounded-lg h-32 mb-2"></div>
        <div className="space-y-2">
          <div className="bg-gray-300 dark:bg-gray-700 rounded h-4 w-full"></div>
          <div className="bg-gray-300 dark:bg-gray-700 rounded h-4 w-2/3"></div>
        </div>
      </div>
    ))}
  </div>
);

export const NewsFourSkeleton: React.FC = () => (
  <div className="space-y-4">
    {/* First item with image */}
    <div className="animate-pulse pb-4 border-b-2 border-gray-200">
      <div className="bg-gray-300 dark:bg-gray-700 rounded-lg h-32 mb-3"></div>
      <div className="space-y-2">
        <div className="bg-gray-300 dark:bg-gray-700 rounded h-5 w-full"></div>
        <div className="bg-gray-300 dark:bg-gray-700 rounded h-4 w-3/4"></div>
        <div className="bg-gray-300 dark:bg-gray-700 rounded h-3 w-1/2"></div>
      </div>
    </div>
    
    {/* Other items */}
    {Array.from({ length: 4 }, (_, index) => (
      <div key={index} className="animate-pulse pb-3 border-b border-gray-100">
        <div className="space-y-2">
          <div className="bg-gray-300 dark:bg-gray-700 rounded h-4 w-full"></div>
          <div className="bg-gray-300 dark:bg-gray-700 rounded h-3 w-2/3"></div>
        </div>
      </div>
    ))}
  </div>
);

export const CategorySkeleton: React.FC = () => (
  <div className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm rounded-xl shadow-lg p-4 sm:p-6">
    <div className="animate-pulse">
      {/* Category title skeleton */}
      <div className="bg-gray-300 dark:bg-gray-700 rounded-lg h-10 w-2/3 mb-6"></div>
      
      {/* Content skeleton */}
      <div className="space-y-4">
        {Array.from({ length: 3 }, (_, index) => (
          <div key={index} className="flex space-x-3">
            <div className="bg-gray-300 dark:bg-gray-700 rounded-lg h-16 w-20 flex-shrink-0"></div>
            <div className="flex-1 space-y-2">
              <div className="bg-gray-300 dark:bg-gray-700 rounded h-4 w-full"></div>
              <div className="bg-gray-300 dark:bg-gray-700 rounded h-3 w-2/3"></div>
            </div>
          </div>
        ))}
      </div>
    </div>
  </div>
);

export default LoadingSkeleton;
