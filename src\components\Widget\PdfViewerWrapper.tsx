"use client";

import dynamic from "next/dynamic";

// Load PDF viewer dynamically
const AdvancedPdfViewer = dynamic(() => import("@/components/Widget/AdvancedPdfViewer"), { ssr: false });

interface PdfViewerWrapperProps {
  fileUrl: string;
  fileName?: string;
}

function PdfViewerWrapper({ fileUrl, fileName = "document.pdf" }: PdfViewerWrapperProps) {
  return (
    <div className="w-full max-w-7xl mx-auto">
      {/* PDF Viewer Content */}
      <div className="border rounded-lg overflow-hidden min-h-[900px] max-w-full bg-white">
        <AdvancedPdfViewer fileUrl={fileUrl} fileName={fileName} />
      </div>
    </div>
  );
}

export default PdfViewerWrapper;
