"use client";

import { useEffect, useState } from "react";
import { useParams, useSearchParams } from "next/navigation";
import Link from "next/link";
import envConfig from "@/config";
import blogApiRequest from "@/apiRequests/blog";
import FileWrap from "@/components/Widget/FileWrap";
import Breadcrumbs from "@/components/Navigation/Breadcrumbs";
import BlogMeta from "@/components/Navigation/BlogMeta";
import ContentWrapClient from "@/components/Widget/ContentWrapClient";

interface PreviewData {
  _id: string;
  title: string;
  desc: string;
  short: string;
  slug: string;
  featureImg: {
    path: string;
    folder: string;
    _id: string;
  };
  file: Array<{
    path: string;
    originalName?: string;
    size?: number;
    type?: string;
  }>;
  video: string;
  categories: string[];
  user: any;
  isPreview: boolean;
  previewTimestamp: number;
  createdAt?: string;
  updatedAt?: string;
}

export default function PreviewPage() {
  const params = useParams();
  const searchParams = useSearchParams();
  const slug = params.slug as string;
  const isPreview = searchParams.get('preview') === 'true';
  
  const [previewData, setPreviewData] = useState<PreviewData | null>(null);
  const [loading, setLoading] = useState(true);

  // Normalize preview data to ensure proper types
  const normalizePreviewData = (data: any): PreviewData => {
    return {
      ...data,
      video: typeof data.video === 'string' ? data.video : (data.video || ""),
      file: Array.isArray(data.file) ? data.file : (data.file ? [data.file] : []),
      categories: Array.isArray(data.categories) ? data.categories : []
    };
  };

  useEffect(() => {
    const loadPreviewData = async () => {
      if (!slug) return;

      if (isPreview) {
        // First try to get from sessionStorage (for unsaved drafts)
        const stored = sessionStorage.getItem(`preview_${slug}`);
        if (stored) {
          try {
            const data = JSON.parse(stored);
            // Check if preview data is not too old (1 hour)
            if (Date.now() - data.previewTimestamp < 3600000) {
              setPreviewData(normalizePreviewData(data));
              setLoading(false);
              return;
            } else {
              // Preview data expired, trying to fetch from server
            }
          } catch (error) {
            // Error parsing preview data handled silently
          }
        }

        // Try to fetch from backend (for saved posts)
        try {
          const sessionToken = localStorage.getItem("sessionToken") || "";
          if (sessionToken) {
            const response = await fetch(`/api/preview?slug=${slug}&preview=true`, {
              headers: {
                'Authorization': `Bearer ${sessionToken}`,
              },
            });

            if (response.ok) {
              const result = await response.json();
              if (result.success) {
                setPreviewData(normalizePreviewData(result.post));
                setLoading(false);
                return;
              }
            }
          }
        } catch (error) {
          // Error fetching preview from server handled silently
        }
      }
      
      setLoading(false);
    };

    loadPreviewData();
  }, [slug, isPreview]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Đang tải bản xem trước...</p>
        </div>
      </div>
    );
  }

  if (!previewData) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-800 mb-4">Không tìm thấy bản xem trước</h1>
          <p className="text-gray-600 mb-6">Dữ liệu xem trước không tồn tại hoặc đã hết hạn.</p>
          <Link 
            href="/dashboard/account/blog/add" 
            className="bg-blue-500 text-white px-6 py-2 rounded hover:bg-blue-600"
          >
            Quay lại tạo bài viết
          </Link>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="first-block bg-white">
        <div className="container mx-auto py-4 px-4 main-section">
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-3 gap-0 md:gap-8 mb-4 relative">
            <div className="main-content col-span-2 bg-white p-4 rounded shadow">
              <div className="content-wrap mb-4 bg-white">
                <div className="relative">
                  <div className="max-w-3xl mx-auto">
                    {/* Breadcrumbs - simplified for preview */}
                    <nav className="text-sm mb-4">
                      <span className="text-gray-500">Trang chủ</span>
                      <span className="mx-2 text-gray-400">/</span>
                      <span className="text-gray-500">Xem trước</span>
                      <span className="mx-2 text-gray-400">/</span>
                      <span className="text-gray-700">{previewData.title}</span>
                    </nav>
                    
                    <div className="flex relative">
                      <div className="flex flex-col justify-between leading-normal w-full">
                        <div className="">
                          <h1 className="text-gray-900 font-bold text-4xl">
                            {previewData.title}
                          </h1>
                          
                          {/* Meta info */}
                          <div className="flex items-center text-sm text-gray-600 my-4">
                            <span>Tác giả: {previewData.user?.username || 'Unknown'}</span>
                            <span className="mx-2">•</span>
                            <span>Slug: <code className="bg-gray-100 px-2 py-1 rounded">{previewData.slug}</code></span>
                          </div>

                          {/* Feature Image */}
                          {previewData.featureImg?.path && (
                            <div className="mb-6">
                              <img
                                src={`${envConfig.NEXT_PUBLIC_API_ENDPOINT}${previewData.featureImg.path}`}
                                alt={previewData.title}
                                className="w-full h-auto object-cover rounded-lg"
                              />
                            </div>
                          )}

                          {/* Short description */}
                          {previewData.short && (
                            <div className="bg-gray-100 p-4 rounded-lg mb-6">
                              <p className="text-gray-700 italic">
                                {previewData.short}
                              </p>
                            </div>
                          )}

                          <div className="content-wrapper overflow-x-hidden bg-white">
                            <ContentWrapClient html={previewData.desc} video={previewData.video} />
                          </div>

                          <FileWrap blog={previewData} />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="sidebar col-span-1">
              <div className="sticky top-12 bg-white p-4 rounded shadow">
                <h2 className="font-semibold text-lg text-red-600 mb-4">� Thông tin bài viết</h2>
                <div className="space-y-3 text-sm">
                  <div>
                    <strong>Tiêu đề:</strong>
                    <p className="text-gray-700">{previewData.title}</p>
                  </div>
                  <div>
                    <strong>Slug:</strong>
                    <p className="text-gray-700 font-mono bg-gray-100 p-1 rounded">{previewData.slug}</p>
                  </div>
                  {previewData.file && previewData.file.length > 0 && (
                    <div>
                      <strong>Files đính kèm:</strong>
                      <p className="text-gray-700">{previewData.file.length} file(s)</p>
                    </div>
                  )}
                  {previewData.video && (
                    <div>
                      <strong>Video:</strong>
                      <p className="text-gray-700">Có video đính kèm</p>
                    </div>
                  )}
                </div>
                
                {/* Actions */}
                <div className="mt-6 space-y-2">
                  <button
                    onClick={() => window.close()}
                    className="w-full bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600 text-sm"
                  >
                    Đóng
                  </button>
                  <Link
                    href={`/dashboard/account/blog/${previewData._id || 'add'}`}
                    className="block w-full bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 text-sm text-center"
                  >
                    Chỉnh sửa
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
