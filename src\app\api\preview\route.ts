import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const slug = searchParams.get('slug');
    const preview = searchParams.get('preview');

    if (!slug || preview !== 'true') {
      return NextResponse.json(
        { success: false, message: "Invalid preview request" },
        { status: 400 }
      );
    }

    // Get authorization header
    const authorization = request.headers.get('authorization');
    if (!authorization) {
      return NextResponse.json(
        { success: false, message: "Authorization required" },
        { status: 401 }
      );
    }

    // Forward request to backend with preview flag
    const backendUrl = `${process.env.NEXT_PUBLIC_API_ENDPOINT}/api/post/preview/${slug}`;
    
    const response = await fetch(backendUrl, {
      method: 'GET',
      headers: {
        'Authorization': authorization,
        'Content-Type': 'application/json',
      },
    });

    const data = await response.json();

    if (!response.ok) {
      return NextResponse.json(
        { success: false, message: data.message || "Failed to fetch preview" },
        { status: response.status }
      );
    }

    return NextResponse.json(data);
  } catch (error) {
    // Console error removed
    return NextResponse.json(
      { success: false, message: "Internal server error" },
      { status: 500 }
    );
  }
}
