import http from "@/lib/http";

export interface Position {
  _id: string;
  name: string;
  description?: string;
  department: {
    _id: string;
    name: string;
  };
  permissions: string[];
  level: number;
  order: number;
  isDefault: boolean;
  isActive: boolean;
  createdBy?: {
    _id: string;
    username: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface CreatePositionRequest {
  name: string;
  description?: string;
  departmentId: string;
  permissions?: string[];
  level?: number;
  order?: number;
}

export interface UpdatePositionRequest {
  name?: string;
  description?: string;
  permissions?: string[];
  level?: number;
  order?: number;
}

export interface PositionResponse {
  success: boolean;
  data: Position;
  message: string;
}

export interface PositionsResponse {
  success: boolean;
  data: Position[];
  message: string;
}

export interface PermissionsResponse {
  success: boolean;
  data: Array<{
    value: string;
    label: string;
  }>;
  message: string;
}

const positionApiRequest = {
  // Get all positions
  getAllPositions: (sessionToken: string) =>
    http.get<PositionsResponse>("/api/positions", {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  // Get positions by department
  getPositionsByDepartment: (departmentId: string, sessionToken: string) =>
    http.get<PositionsResponse>(`/api/positions/department/${departmentId}`, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  // Get position by ID
  getPositionById: (id: string, sessionToken: string) =>
    http.get<PositionResponse>(`/api/positions/${id}`, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  // Create position
  createPosition: (body: CreatePositionRequest, sessionToken: string) =>
    http.post<PositionResponse>("/api/positions", body, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  // Update position
  updatePosition: (id: string, body: UpdatePositionRequest, sessionToken: string) =>
    http.put<PositionResponse>(`/api/positions/${id}`, body, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  // Delete position
  deletePosition: (id: string, sessionToken: string) =>
    http.delete<{ success: boolean; message: string }>(`/api/positions/${id}`, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  // Get available permissions
  getAvailablePermissions: (sessionToken: string) =>
    http.get<PermissionsResponse>("/api/positions/permissions", {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),
};

export default positionApiRequest;
