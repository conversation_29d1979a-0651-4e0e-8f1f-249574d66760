const express = require('express');
const router = express.Router();
const fileController = require('../controllers/fileManager');
const passport = require('passport');
const { mongoIdValidation } = require('../middleware/inputValidation');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

// Ensure upload directories exist
const uploadDirs = ['uploads/images', 'uploads/videos', 'uploads/documents'];
uploadDirs.forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
});

// Multer configuration for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    let uploadPath = 'uploads/';
    
    if (file.mimetype.startsWith('image/')) {
      uploadPath += 'images/';
    } else if (file.mimetype.startsWith('video/')) {
      uploadPath += 'videos/';
    } else {
      uploadPath += 'documents/';
    }
    
    cb(null, uploadPath);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, file.fieldname + '-' + uniqueSuffix + ext);
  }
});

const fileFilter = (req, file, cb) => {
  // Allow common file types
  const allowedTypes = [
    // Images
    'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp',
    // Videos
    'video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/webm',
    // Documents
    'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/plain', 'text/csv'
  ];
  
  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('File type not allowed'), false);
  }
};

const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 100 * 1024 * 1024, // 100MB limit
  }
});

// File upload endpoint
router.post('/upload', passport.authenticate('user', { session: false }), upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ success: false, message: 'No file uploaded' });
    }

    const File = require('../models/file');
    
    // Create file record in database
    const fileRecord = new File({
      filename: req.file.filename,
      originalName: req.file.originalname,
      mimetype: req.file.mimetype,
      size: req.file.size,
      path: req.file.path,
      url: `/api/files/serve/${req.file.filename}`,
      uploadedBy: req.user._id,
      description: req.body.description || '',
      tags: req.body.tags ? req.body.tags.split(',').map(tag => tag.trim()) : []
    });

    await fileRecord.save();

    // Update URL with actual file ID
    fileRecord.url = `/api/files/serve/${fileRecord._id}`;
    await fileRecord.save();

    res.json({
      success: true,
      file: fileRecord,
      message: 'File uploaded successfully'
    });

  } catch (error) {
    // Console error removed
    
    // Clean up uploaded file if database save failed
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }
    
    res.status(500).json({ 
      success: false, 
      message: error.message || 'Internal server error' 
    });
  }
});

// Protected routes (require authentication)
router.use(passport.authenticate('user', { session: false }));

// Get all files with filters and pagination
router.post('/', fileController.getFiles);

// Get file statistics
router.get('/stats', fileController.getFileStats);

// Get sync status
router.get('/sync-status', fileController.getSyncStatus);

// Sync existing files
router.post('/sync-existing', fileController.syncExistingFiles);

// Search files
router.get('/search', fileController.searchFiles);

// Bulk actions on files
router.post('/bulk-action', fileController.bulkAction);

// Get single file by ID
router.get('/:id', mongoIdValidation, fileController.getFileById);

// Update file metadata
router.put('/:id', mongoIdValidation, fileController.updateFile);

// Delete single file
router.delete('/:id', mongoIdValidation, fileController.deleteFile);

module.exports = router;
