"use client";
import Link from "next/link";
import { Home, Search, Menu } from "react-feather";
import ProfileDropdown from "@/components/Navigation/ProfileDropdown";
import { usePathname } from "next/navigation";
import { useState } from "react";

const Header: React.FC = () => {
  const pathname = usePathname();
  const [showSearch, setShowSearch] = useState(false);

  // Get page title based on current route
  const getPageTitle = () => {
    if (pathname === "/dashboard") return "Tổng quan";
    if (pathname.includes("/blog")) return "Quản lý tin tức";
    if (pathname.includes("/user")) return "Quản lý thành viên";
    if (pathname.includes("/categories")) return "Quản lý danh mục";
    if (pathname.includes("/page")) return "Quản lý trang";
    if (pathname.includes("/menu")) return "Quản lý menu";
    if (pathname.includes("/setting")) return "Cài đặt";
    if (pathname.includes("/schedule")) return "Lịch xét xử";
    if (pathname.includes("/account")) return "Thông tin tài khoản";
    return "Dashboard";
  };

  return (
    <header className="w-full bg-white shadow-sm border-b border-gray-200 sticky top-0 z-40">
      <div className="px-6 py-4">
        <div className="flex items-center justify-between">
          {/* Left Section - Page Title & Breadcrumb */}
          <div className="flex items-center space-x-4">
            <div className="md:hidden">
              <button className="p-2 rounded-lg hover:bg-gray-100">
                <Menu size={20} className="text-gray-600" />
              </button>
            </div>

            <div>
              <h1 className="text-xl font-semibold text-gray-900">
                {getPageTitle()}
              </h1>
              <div className="flex items-center text-sm text-gray-500 mt-1">
                <Link href="/" className="flex items-center hover:text-blue-600 transition-colors">
                  <Home size={14} className="mr-1" />
                  Trang chủ
                </Link>
                <span className="mx-2">•</span>
                <span>Dashboard</span>
              </div>
            </div>
          </div>

          {/* Right Section - Search, Notifications, Profile */}
          <div className="flex items-center space-x-4">
            {/* Search */}
            <div className="relative hidden md:block">
              {showSearch ? (
                <div className="flex items-center">
                  <input
                    type="text"
                    placeholder="Tìm kiếm..."
                    className="w-64 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    autoFocus
                    onBlur={() => setShowSearch(false)}
                  />
                </div>
              ) : (
                <button
                  onClick={() => setShowSearch(true)}
                  className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <Search size={20} className="text-gray-600" />
                </button>
              )}
            </div>



            {/* Profile Dropdown */}
            <ProfileDropdown />
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
