"use client";

import { useState, useEffect } from "react";
import { FileItemType } from "@/schemaValidations/file.schema";
import { formatBytes, formatDate } from "@/utils/formatters";
import { toast } from "react-toastify";

interface FileListProps {
  files: FileItemType[];
  onFileSelect: (file: FileItemType) => void;
  onFileDelete: (fileId: string) => void;
  onBulkAction: (fileIds: string[], action: "delete" | "activate" | "deactivate") => void;
  loading?: boolean;
}

const FileList: React.FC<FileListProps> = ({
  files,
  onFileSelect,
  onFileDelete,
  onBulkAction,
  loading = false
}) => {
  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);
  const [selectAll, setSelectAll] = useState(false);

  // Handle select all
  const handleSelectAll = () => {
    if (selectAll) {
      setSelectedFiles([]);
    } else {
      setSelectedFiles(files.map(file => file._id));
    }
    setSelectAll(!selectAll);
  };

  // Handle individual file selection
  const handleFileSelect = (fileId: string) => {
    setSelectedFiles(prev => {
      if (prev.includes(fileId)) {
        return prev.filter(id => id !== fileId);
      } else {
        return [...prev, fileId];
      }
    });
  };

  // Update select all state when files change
  useEffect(() => {
    setSelectAll(selectedFiles.length === files.length && files.length > 0);
  }, [selectedFiles, files]);

  // Get file type icon
  const getFileIcon = (type: string, mimetype: string) => {
    switch (type) {
      case 'image':
        return '🖼️';
      case 'video':
        return '🎬';
      case 'document':
        if (mimetype.includes('pdf')) return '📄';
        if (mimetype.includes('word')) return '📝';
        if (mimetype.includes('excel')) return '📊';
        return '📄';
      default:
        return '📁';
    }
  };

  // Handle bulk actions
  const handleBulkAction = (action: "delete" | "activate" | "deactivate") => {
    if (selectedFiles.length === 0) {
      toast.warning("Please select files first");
      return;
    }

    const confirmMessage = action === 'delete' 
      ? `Are you sure you want to delete ${selectedFiles.length} files?`
      : `Are you sure you want to ${action} ${selectedFiles.length} files?`;

    if (confirm(confirmMessage)) {
      onBulkAction(selectedFiles, action);
      setSelectedFiles([]);
      setSelectAll(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2">Loading files...</span>
      </div>
    );
  }

  if (files.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        <div className="text-4xl mb-2">📁</div>
        <p>No files found</p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow">
      {/* Bulk Actions Bar */}
      {selectedFiles.length > 0 && (
        <div className="bg-blue-50 border-b px-4 py-3 flex items-center justify-between">
          <span className="text-sm text-blue-700">
            {selectedFiles.length} files selected
          </span>
          <div className="flex gap-2">
            <button
              onClick={() => handleBulkAction('activate')}
              className="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700"
            >
              Activate
            </button>
            <button
              onClick={() => handleBulkAction('deactivate')}
              className="px-3 py-1 bg-yellow-600 text-white rounded text-sm hover:bg-yellow-700"
            >
              Deactivate
            </button>
            <button
              onClick={() => handleBulkAction('delete')}
              className="px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700"
            >
              Delete
            </button>
          </div>
        </div>
      )}

      {/* File Table */}
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-gray-50 border-b">
            <tr>
              <th className="px-4 py-3 text-left">
                <input
                  type="checkbox"
                  checked={selectAll}
                  onChange={handleSelectAll}
                  className="rounded border-gray-300"
                />
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                File
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Type
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Size
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Uploaded By
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Date
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {files.map((file) => (
              <tr
                key={file._id}
                className={`hover:bg-gray-50 ${selectedFiles.includes(file._id) ? 'bg-blue-50' : ''}`}
              >
                <td className="px-4 py-3">
                  <input
                    type="checkbox"
                    checked={selectedFiles.includes(file._id)}
                    onChange={() => handleFileSelect(file._id)}
                    className="rounded border-gray-300"
                  />
                </td>
                <td className="px-4 py-3">
                  <div className="flex items-center">
                    <span className="text-2xl mr-3">
                      {getFileIcon(file.type, file.mimetype)}
                    </span>
                    <div>
                      <div className="text-sm font-medium text-gray-900 truncate max-w-xs">
                        {file.originalName}
                      </div>
                      <div className="text-xs text-gray-500 truncate max-w-xs">
                        {file.filename}
                      </div>
                    </div>
                  </div>
                </td>
                <td className="px-4 py-3 text-sm text-gray-900">
                  <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                    {file.type}
                  </span>
                </td>
                <td className="px-4 py-3 text-sm text-gray-900">
                  {formatBytes(file.size)}
                </td>
                <td className="px-4 py-3 text-sm text-gray-900">
                  {file.uploadedBy.username}
                </td>
                <td className="px-4 py-3 text-sm text-gray-900">
                  {formatDate(file.uploadedAt)}
                </td>
                <td className="px-4 py-3">
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    file.isActive 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {file.isActive ? 'Active' : 'Inactive'}
                  </span>
                </td>
                <td className="px-4 py-3 text-sm font-medium">
                  <div className="flex gap-2">
                    <button
                      onClick={() => onFileSelect(file)}
                      className="text-blue-600 hover:text-blue-900"
                    >
                      View
                    </button>
                    <button
                      onClick={() => window.open(file.url, '_blank')}
                      className="text-green-600 hover:text-green-900"
                    >
                      Download
                    </button>
                    <button
                      onClick={() => onFileDelete(file._id)}
                      className="text-red-600 hover:text-red-900"
                    >
                      Delete
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default FileList;
