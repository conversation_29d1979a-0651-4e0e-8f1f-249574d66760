import { Node, nodeInputRule } from '@tiptap/react'
import { Plug<PERSON>, Plugin<PERSON>ey } from 'prosemirror-state'

export interface VideoOptions {
  HTMLAttributes: Record<string, any>,
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    video: {
      /**
       * Set a video node
       */
      setVideo: (src: string) => ReturnType,
      /**
       * Toggle a video
       */
      toggleVideo: (src: string) => ReturnType,
    }
  }
}

const VIDEO_INPUT_REGEX = /!\[(.+|:?)]\((\S+)(?:(?:\s+)["'](\S+)["'])?\)/

export const Video = Node.create({
  name: 'video',

  group: "block",

  selectable: true,

  draggable: true,

  addAttributes() {
    return {
      src: {
        default: null,
        parseHTML: (el) => (el as HTMLVideoElement).getAttribute('src'),
        renderHTML: (attrs) => ({ src: attrs.src }),
      },
      controls: {
        default: true,
        parseHTML: (el) => (el as HTMLVideoElement).hasAttribute('controls'),
        renderHTML: (attrs) => attrs.controls ? { controls: 'true' } : {},
      },
      preload: {
        default: 'metadata',
        parseHTML: (el) => (el as HTMLVideoElement).getAttribute('preload'),
        renderHTML: (attrs) => ({ preload: attrs.preload }),
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: 'video',
        getAttrs: el => {
          const element = el as HTMLVideoElement;
          const src = element.getAttribute('src');

          if (src) {
            // Ensure video has required classes when parsing
            element.classList.add('video-player', 'prosemirror-video');
            element.setAttribute('controls', 'true');
            element.setAttribute('preload', 'metadata');

            return {
              src,
              controls: true,
              preload: 'metadata'
            };
          }

          return false;
        },
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return [
      'video',
      {
        class: 'video-player prosemirror-video',
        style: 'width: 100%; max-width: 100%; height: auto;',
        controls: 'true',
        preload: 'metadata',
        ...HTMLAttributes
      },
      ['source', HTMLAttributes]
    ]
  },

  addCommands() {
    return {
      setVideo: (src: string) => ({ commands }) => {
        return commands.insertContent({
          type: this.name,
          attrs: {
            src,
            controls: true,
            preload: 'metadata',
          },
        });
      },

      toggleVideo: () => ({ commands }) => commands.toggleNode(this.name, 'paragraph'),
    };
  },

  addInputRules() {
    return [
      nodeInputRule({
        find: VIDEO_INPUT_REGEX,
        type: this.type,
        getAttributes: (match) => {
          const [,, src] = match

          return { src }
        },
      })
    ]
  },

  addProseMirrorPlugins() {
    return [
      new Plugin({
        key: new PluginKey('videoDropPlugin'),

        props: {
          handleDOMEvents: {
            drop(view, event) {
              const { state: { schema, tr }, dispatch } = view
              const hasFiles = event.dataTransfer &&
                event.dataTransfer.files &&
                event.dataTransfer.files.length

              if (!hasFiles) return false

              const videos = Array
                .from(event.dataTransfer.files)
                .filter(file => (/video/i).test(file.type))

              if (videos.length === 0) return false

              event.preventDefault()

              const coordinates = view.posAtCoords({ left: event.clientX, top: event.clientY })

              videos.forEach(video => {
                const reader = new FileReader()

                reader.onload = readerEvent => {
                  const node = schema.nodes.video.create({ src: readerEvent.target?.result })

                  if (coordinates && typeof coordinates.pos === 'number') {
                    const transaction = tr.insert(coordinates?.pos, node)

                    dispatch(transaction)
                  }
                }

                reader.readAsDataURL(video)
              })

              return true
            }
          }
        }
      })
    ]
  }

})
