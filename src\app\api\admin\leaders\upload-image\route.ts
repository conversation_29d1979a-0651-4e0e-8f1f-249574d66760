import { NextRequest, NextResponse } from "next/server";

// POST - Upload leader image (admin only)
export async function POST(request: NextRequest) {
  try {
    const token = request.headers.get("authorization")?.replace("Bearer ", "");
    if (!token) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    const formData = await request.formData();
    const file = formData.get("file") as File;

    if (!file) {
      return NextResponse.json(
        { message: "Không tìm thấy file" },
        { status: 400 }
      );
    }

    // Validate file type
    const allowedTypes = ["image/jpeg", "image/jpg", "image/png", "image/webp"];
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { message: "Định dạng file không được hỗ trợ. Chỉ chấp nhận JPG, PNG, WEBP" },
        { status: 400 }
      );
    }

    // Validate file size (5MB)
    const maxSize = 5 * 1024 * 1024;
    if (file.size > maxSize) {
      return NextResponse.json(
        { message: "Kích thước file không được vượt quá 5MB" },
        { status: 400 }
      );
    }

    // Forward to backend API for actual upload
    const backendUrl = process.env.BACKEND_URL || "http://localhost:8000";
    const backendFormData = new FormData();
    backendFormData.append("imageFile", file);

    const response = await fetch(`${backendUrl}/api/media/single`, {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${token}`,
      },
      body: backendFormData,
    });

    const result = await response.json();

    if (!response.ok) {
      return NextResponse.json(
        { message: result.message || "Lỗi khi tải ảnh lên backend" },
        { status: response.status }
      );
    }

    return NextResponse.json({
      url: result.media?.path || result.featureImg?.path || result.url,
      message: "Tải ảnh lên thành công"
    });

  } catch (error) {
    // Console error removed
    return NextResponse.json(
      { message: "Lỗi server khi tải ảnh lên" },
      { status: 500 }
    );
  }
}
