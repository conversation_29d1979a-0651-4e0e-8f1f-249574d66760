import { NextRequest, NextResponse } from "next/server";

// PUT - Reorder leaders (admin only)
export async function PUT(request: NextRequest) {
  try {
    // TODO: Add authentication middleware
    // const token = request.headers.get("authorization")?.replace("Bearer ", "");
    // if (!token || !isValidAdminToken(token)) {
    //   return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    // }

    const body = await request.json();
    const { leaders } = body;

    if (!Array.isArray(leaders)) {
      return NextResponse.json(
        { message: "Dữ liệu không hợp lệ" },
        { status: 400 }
      );
    }

    // Validate each item has id and order
    for (const leader of leaders) {
      if (!leader.id || typeof leader.order !== "number") {
        return NextResponse.json(
          { message: "Dữ liệu không hợp lệ. Mỗi mục phải có id và order" },
          { status: 400 }
        );
      }
    }

    // TODO: Update order in database
    // await update<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(leaders);

    return NextResponse.json({
      message: "<PERSON><PERSON><PERSON> nhật thứ tự lãnh đạo thành công"
    });

  } catch (error) {
    // Console error removed
    return NextResponse.json(
      { message: "Lỗi server khi cập nhật thứ tự lãnh đạo" },
      { status: 500 }
    );
  }
}
