/* Homepage specific styles */

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(228, 57, 60, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(228, 57, 60, 0.8);
  }
}

/* Animation classes */
.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out forwards;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* Optimize animations for reduced motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Improve focus visibility for accessibility */
*:focus-visible {
  outline: 2px solid #e4393c;
  outline-offset: 2px;
  border-radius: 4px;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #e4393c;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #d32f2f;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .bg-white\/95 {
    background-color: white !important;
  }
  
  .bg-gray-900\/95 {
    background-color: black !important;
    color: white !important;
  }
  
  .text-gray-600 {
    color: black !important;
  }
  
  .shadow-lg {
    border: 2px solid black !important;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .bg-white\/95,
  .bg-gray-900\/95 {
    background: white !important;
    color: black !important;
  }
  
  .shadow-lg,
  .shadow-xl {
    box-shadow: none !important;
    border: 1px solid #ccc !important;
  }
}

/* Loading skeleton animations */
@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.skeleton-loading {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.5s infinite;
}

/* Dark mode specific styles */
@media (prefers-color-scheme: dark) {
  .skeleton-loading {
    background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
    background-size: 200px 100%;
  }
}

/* Hover effects */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* Glass morphism effect */
.glass-morphism {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

@media (prefers-color-scheme: dark) {
  .glass-morphism {
    background: rgba(17, 24, 39, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
}

/* Gradient text */
.gradient-text {
  background: linear-gradient(135deg, #e4393c 0%, #d32f2f 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Custom button styles */
.btn-primary {
  background: linear-gradient(135deg, #e4393c 0%, #d32f2f 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(228, 57, 60, 0.25);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 15px rgba(228, 57, 60, 0.35);
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(228, 57, 60, 0.25);
}

/* Responsive typography */
@media (max-width: 640px) {
  .responsive-text-lg {
    font-size: 1rem;
    line-height: 1.5rem;
  }
}

@media (min-width: 641px) {
  .responsive-text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }
}

@media (min-width: 1024px) {
  .responsive-text-lg {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
}

/* Loading states */
.loading-shimmer {
  position: relative;
  overflow: hidden;
}

.loading-shimmer::after {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  transform: translateX(-100%);
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.6),
    transparent
  );
  animation: shimmer 2s infinite;
  content: '';
}

@keyframes shimmer {
  100% {
    transform: translateX(100%);
  }
}

/* Accessibility improvements */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Skip to content link */
.skip-to-content {
  position: absolute;
  top: -40px;
  left: 6px;
  background: #e4393c;
  color: white;
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 1000;
  transition: top 0.3s;
}

.skip-to-content:focus {
  top: 6px;
}

/* Ads content styling */
.ads-content {
  max-width: 100%;
  overflow: hidden;
}

.ads-content img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
}

.ads-content iframe {
  max-width: 100%;
  border-radius: 8px;
}

/* Responsive ads */
@media (max-width: 768px) {
  .ads-content {
    text-align: center;
  }

  .ads-content img {
    max-height: 120px;
    object-fit: contain;
  }
}

@media (min-width: 769px) {
  .ads-content img {
    max-height: 200px;
    object-fit: contain;
  }
}
