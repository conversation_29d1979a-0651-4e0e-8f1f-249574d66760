"use client";
import RenderImage from "@/components/Widget/renderImage";
import Link from "next/link";
type Blog = {
  _id: string;
  slug: string;
  title: string;
  short?: string;
  featureImg?: { path: string };
};

interface NewsOneProps {
  blogs: Blog[];
}

const NewsOne: React.FC<NewsOneProps> = ({ blogs }) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-3 gap-4 sm:gap-6">
      {blogs.map((item, index) => {
        let colSpan = "col-span-1";
        let rowSpan = "row-span-1";
        let infocontent = null;
        let isMainStory = index === 0;

        if (isMainStory) {
          colSpan = "col-span-1 md:col-span-3 grid grid-cols-1 md:grid-cols-5 gap-4 md:gap-6";
          rowSpan = "row-span-2";
          infocontent = (
            <p key={`info-${item._id}`} className="text-gray-600 dark:text-gray-300 line-clamp-3 leading-relaxed mt-2">
              {item.short}
            </p>
          );
        }

        return (
          <div
            key={item._id || `news-${index}`}
            className={`${colSpan} ${rowSpan} group transition-all duration-300 hover:transform hover:scale-[1.02]`}
          >
            <div className={`single-post-thumbnail ${isMainStory ? 'col-span-3' : ''} mb-3 overflow-hidden rounded-lg shadow-md group-hover:shadow-lg transition-all duration-300`}>
              <Link href={"blog/" + item.slug}>
                <div className="relative overflow-hidden rounded-lg">
                  <RenderImage
                    img_url={item.featureImg?.path}
                    title={item.title}
                    categories={item.categories || []}
                  />
                  {/* Overlay effect */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                  {/* Category badge for main story */}
                  {isMainStory && item.categories && item.categories.length > 0 && (
                    <div className="absolute top-3 left-3 bg-red-600 text-white px-3 py-1 rounded-full text-xs font-semibold shadow-lg">
                      {item.categories[0].name}
                    </div>
                  )}
                </div>
              </Link>
            </div>

            <div className={`box-text ${isMainStory ? 'col-span-2' : ''} transition-all duration-300`}>
              <Link
                href={"blog/" + item.slug}
                className={`${isMainStory ? 'text-xl md:text-2xl' : 'text-md'} font-semibold line-clamp-2 hover:text-red-600 transition-colors duration-300 block mb-2`}
              >
                {item.title}
              </Link>

              {/* Reading time and date for main story */}
              {isMainStory && (
                <div className="flex items-center text-sm text-gray-500 dark:text-gray-400 mb-2 space-x-4">
                  <span className="flex items-center">
                    <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                    </svg>
                    5 phút đọc
                  </span>
                  <span className="flex items-center">
                    <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                    </svg>
                    Hôm nay
                  </span>
                </div>
              )}

              {infocontent}

              {/* Read more link for main story */}
              {isMainStory && (
                <Link
                  href={"blog/" + item.slug}
                  className="inline-flex items-center text-red-600 hover:text-red-700 font-medium text-sm mt-3 transition-colors duration-300"
                >
                  Đọc tiếp
                  <svg className="w-4 h-4 ml-1 transition-transform duration-300 group-hover:translate-x-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </Link>
              )}
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default NewsOne;
