"use client";
import { useAppContext } from "@/app/app-provider";
import { useAuth } from "@/hooks/useAuth";
import { useState, useEffect, useCallback } from "react";
import {
  Users,
  FileText,
  Calendar,
  Settings,
  TrendingUp,
  Eye,
  Edit,
  Plus
} from "react-feather";

const Dashboard = () => {
  const { user } = useAppContext();
  const { hasPermission } = useAuth();

  const StatCard = ({ title, value, icon: Icon, color, trend }: {
    title: string;
    value: number;
    icon: any;
    color: string;
    trend?: string;
  }) => (
    <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600 mb-1">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{value.toLocaleString()}</p>
          {trend && (
            <p className="text-sm text-green-600 mt-1 flex items-center">
              <TrendingUp size={14} className="mr-1" />
              {trend}
            </p>
          )}
        </div>
        <div className={`p-3 rounded-lg ${color}`}>
          <Icon size={24} className="text-white" />
        </div>
      </div>
    </div>
  );

  const QuickAction = ({ title, description, href, icon: Icon, color }: {
    title: string;
    description: string;
    href: string;
    icon: any;
    color: string;
  }) => (
    <a
      href={href}
      className="block bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-all hover:scale-105"
    >
      <div className="flex items-start space-x-4">
        <div className={`p-3 rounded-lg ${color}`}>
          <Icon size={20} className="text-white" />
        </div>
        <div>
          <h3 className="font-semibold text-gray-900 mb-1">{title}</h3>
          <p className="text-sm text-gray-600">{description}</p>
        </div>
      </div>
    </a>
  );

  return (
    <div className="space-y-8">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl text-white p-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold mb-2">
              Chào mừng trở lại, {user?.username}!
            </h1>
            <p className="text-blue-100 text-lg">
              Đây là tổng quan về hoạt động của bạn hôm nay
            </p>
          </div>
          <div className="hidden md:block">
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
              <Calendar size={48} className="text-white" />
            </div>
          </div>
        </div>
      </div>



      {/* Quick Actions */}
      <div>
        <h2 className="text-xl font-bold text-gray-900 mb-6">Thao tác nhanh</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {hasPermission("admin") && (
            <>
              <QuickAction
                title="Thêm bài viết mới"
                description="Tạo bài viết mới cho website"
                href="/dashboard/secret/blog/add"
                icon={Plus}
                color="bg-blue-500"
              />
              <QuickAction
                title="Quản lý thành viên"
                description="Xem và quản lý tài khoản người dùng"
                href="/dashboard/secret/user"
                icon={Users}
                color="bg-green-500"
              />
              <QuickAction
                title="Cài đặt hệ thống"
                description="Cấu hình và tùy chỉnh website"
                href="/dashboard/secret/setting"
                icon={Settings}
                color="bg-purple-500"
              />
            </>
          )}

          {hasPermission("user") && (
            <QuickAction
              title="Bài viết của tôi"
              description="Quản lý bài viết cá nhân"
              href="/dashboard/account/blog"
              icon={Edit}
              color="bg-indigo-500"
            />
          )}

          {hasPermission("manager") && (
            <QuickAction
              title="Lịch xét xử"
              description="Quản lý lịch xét xử"
              href="/dashboard/manager/schedule"
              icon={Calendar}
              color="bg-red-500"
            />
          )}
        </div>
      </div>

      {/* User Info Card */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <h2 className="text-xl font-bold text-gray-900 mb-4">Thông tin tài khoản</h2>
        {user && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-600 mb-1">Tên người dùng</label>
              <p className="text-lg text-gray-900">{user.username}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-600 mb-1">Email</label>
              <p className="text-lg text-gray-900">{user.email}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-600 mb-1">Vai trò</label>
              <span className={`inline-flex px-3 py-1 rounded-full text-sm font-medium ${
                user.rule === 'admin' ? 'bg-red-100 text-red-800' :
                user.rule === 'manager' ? 'bg-blue-100 text-blue-800' :
                'bg-green-100 text-green-800'
              }`}>
                {user.rule === 'admin' ? 'Quản trị viên' :
                 user.rule === 'manager' ? 'Quản lý' : 'Người dùng'}
              </span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Dashboard;
