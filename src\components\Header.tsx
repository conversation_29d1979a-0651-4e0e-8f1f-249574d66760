"use client";

import { usePathname } from "next/navigation";
import React, { useState, useEffect } from "react";
import { User, Menu, X, Search, ChevronUp } from "react-feather";
import BottomMenu from "@/components/Navigation/BottomMenu";
import SideMobileMenu from "@/components/Navigation/SideMobileMenu";
import { useAppContext } from "@/app/app-provider";
import SearchNormal from "@/components/Navigation/SearchNormal";
import ProfileDropdown from "@/components/Navigation/ProfileDropdown";
import Link from "next/link";
import Image from "next/image";

/**
 * Header component for Ho Chi Minh City People's Court
 */
const VietnameseHeader: React.FC = () => {
  const { user } = useAppContext();
  const pathname = usePathname();
  const [isMobile, setIsMobile] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [showBackToTop, setShowBackToTop] = useState(false);

  useEffect(() => {
    document.body.style.position = "relative";
    document.body.style.overflowY = "auto";

    // Function to check if the screen width is mobile
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // Function to handle scroll events
    const handleScroll = () => {
      const scrollTop = window.scrollY;
      setIsScrolled(scrollTop > 50);
      setShowBackToTop(scrollTop > 300);
    };

    checkMobile(); // Initial check
    handleScroll(); // Initial scroll check

    window.addEventListener("resize", checkMobile);
    window.addEventListener("scroll", handleScroll);

    return () => {
      window.removeEventListener("resize", checkMobile);
      window.removeEventListener("scroll", handleScroll);
    };
  }, [pathname]);

  const getVietnamDate = () => {
    const date = new Date();
    const options: Intl.DateTimeFormatOptions = {
      weekday: "long",
      day: "numeric",
      month: "numeric",
      year: "numeric",
      timeZone: "Asia/Ho_Chi_Minh",
    };
    return new Intl.DateTimeFormat("vi-VN", options).format(date);
  };

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  const isPrivateRoute = pathname.startsWith("/dashboard");

  return (
    <>
      {!isPrivateRoute && (
        <>
          {/* Vietnamese Supreme Court Header */}
          <header className={`w-full hidden md:block transition-all duration-300 ${isScrolled ? 'shadow-lg' : ''}`} style={{ height: "130px" }}>
            <div
              className="relative w-full h-full bg-gradient-to-t from-white via-yellow-50 to-yellow-100 overflow-hidden"
              style={{
                backgroundImage: "-webkit-gradient(linear, 0% 100%, 0% 0%, color-stop(0.2, rgb(255, 255, 255)), color-stop(0.8, rgb(255, 251, 213)))"
              }}
            >
              {/* Background pattern */}
              <div className="absolute inset-0 opacity-10">
                <div className="absolute inset-0 bg-gradient-to-r from-blue-50 to-red-50"></div>
              </div>

              <div
                className="container mx-auto relative h-full flex items-center transition-all duration-300 hover:scale-[1.01]"
                style={{
                  background: "url(/trongdong_1546498623413.png) no-repeat",
                  backgroundPosition: "center",
                  backgroundSize: "contain"
                }}
              >
                <div className="flex items-center ml-4 group">
                  <div className="transition-transform duration-300 group-hover:scale-110">
                    <Link href="/">
                      <Image
                        src="/favicon.ico"
                        alt="Quốc huy"
                        width={90}
                        height={97}
                        className="drop-shadow-md hover:drop-shadow-lg transition-all duration-300"
                        priority
                      />
                    </Link>
                  </div>
                  <div className="flex flex-col ml-3">
                    <div className="text-base md:text-lg text-blue-500 font-medium tracking-wide transition-colors duration-300 hover:text-blue-600">
                      CỔNG THÔNG TIN ĐIỆN TỬ
                    </div>
                    <div className="text-red-600 transition-colors duration-300 hover:text-red-700" style={{
                      fontFamily: '"Roboto Condensed", sans-serif',
                      fontSize: "44px",
                      fontWeight: 700,
                      fontStyle: "normal",
                      lineHeight: "48.4px",
                      textShadow: "1px 1px 2px rgba(0,0,0,0.1)"
                    }}>
                      TÒA ÁN NHÂN DÂN TP.HỒ CHÍ MINH
                    </div>
                  </div>
                </div>

                {/* Enhanced right side image */}
                <div className="absolute right-0 top-0 h-full overflow-hidden">
                  <Image
                    src="/bg-header_1546498587110.png"
                    alt="Hình ảnh Tòa án"
                    width={200}
                    height={130}
                    className="h-full object-cover transition-transform duration-500 hover:scale-105"
                    style={{ height: "130px" }}
                  />
                </div>

                {/* Date and user info */}
                <div className="absolute right-4 top-4 z-10">
                  <div className="text-right bg-white/80 backdrop-blur-sm rounded-lg px-3 py-2 shadow-sm">
                    <div className="text-blue-600 font-medium text-sm">
                      {getVietnamDate()}
                    </div>
                    {user && (
                      <div className="text-blue-700 font-semibold text-sm mt-1">
                        Xin chào, {user.displayName}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </header>

          {/* Enhanced Navigation */}
          <nav
            id="top-nav"
            className={`w-full z-20 border-b border-gray-200 dark:border-gray-600 transition-all duration-300 ${
              isScrolled ? 'bg-white/95 backdrop-blur-md shadow-md' : 'bg-white'
            }`}
          >
            <div className="container flex flex-wrap items-center justify-between mx-auto px-4">
              {/* Enhanced Mobile Logo */}
              <div className="block md:hidden">
                <div className="flex items-center py-2 group">
                  <div className="transition-transform duration-300 group-hover:scale-110">
                    <Link href="/">
                      <Image
                        src="/favicon.ico"
                        alt="Quốc huy"
                        width={60}
                        height={65}
                        className="drop-shadow-sm hover:drop-shadow-md transition-all duration-300"
                      />
                    </Link>
                  </div>
                  <div className="flex flex-col ml-2">
                    <div className="text-xs text-blue-500 font-medium tracking-wide">CỔNG THÔNG TIN ĐIỆN TỬ</div>
                    <div className="text-sm font-bold text-red-600 leading-tight">TÒA ÁN NHÂN DÂN THÀNH PHỐ HỒ CHÍ MINH</div>
                  </div>
                </div>
              </div>
              
              <div className="w-1/3 hidden md:block"></div>
              <div className="right-block flex menu-search items-center justify-end w-auto ml-auto">
                {/* Enhanced Mobile user profile button */}
                {user && (
                  <span className="md:hidden block">
                    <div className="bg-blue-50 rounded-full p-1 hover:bg-blue-100 transition-colors duration-200">
                      <ProfileDropdown />
                    </div>
                  </span>
                )}

                {/* Enhanced Desktop user profile */}
                <span className="md:block hidden ml-2">
                  {user && (
                    <div className="bg-blue-50 rounded-full p-1 hover:bg-blue-100 transition-colors duration-200">
                      <ProfileDropdown />
                    </div>
                  )}
                </span>

                <div className="flex md:order-2 space-x-3 md:space-x-0 rtl:space-x-reverse">
                  <div className="drawer ml-2 block md:hidden">
                    <input id="my-drawer" type="checkbox" className="drawer-toggle" />
                    <div className="drawer-content">
                      <label htmlFor="my-drawer" className="cursor-pointer p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                        <Menu className="w-6 h-6 text-gray-700" />
                      </label>
                    </div>
                    <div className="drawer-side z-50 overflow-y-auto">
                      <label htmlFor="my-drawer" aria-label="close sidebar" className="drawer-overlay"></label>
                      <div className="menu bg-white text-gray-800 min-h-full md:w-2/3 lg:w-1/2 w-full p-4 shadow-2xl">
                        <label
                          htmlFor="my-drawer"
                          aria-label="close sidebar"
                          className="fixed right-4 top-4 w-10 h-10 bg-white rounded-full shadow-lg cursor-pointer flex items-center justify-center hover:bg-gray-50 transition-colors duration-200 z-10"
                        >
                          <X className="w-5 h-5 text-gray-600" />
                        </label>
                        <div className="mt-12">
                          <SearchNormal />
                          <SideMobileMenu />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <BottomMenu />
          </nav>

          {/* Back to Top Button */}
          {showBackToTop && (
            <button
              onClick={scrollToTop}
              className="fixed bottom-6 right-6 z-50 bg-red-600 hover:bg-red-700 text-white p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110"
              aria-label="Về đầu trang"
            >
              <ChevronUp className="w-6 h-6" />
            </button>
          )}
        </>
      )}
    </>
  );
};

export default VietnameseHeader;
