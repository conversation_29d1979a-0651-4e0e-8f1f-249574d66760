import NewsOne from "@/components/Block/NewsOne";
import NewsTwo from "@/components/Block/NewsTwo";
import NewsThree from "@/components/Block/NewsThree";
import NewsFour from "@/components/Block/NewsFour";
import NewsFive from "@/components/Block/NewsFive";
import NewsSix from "@/components/Block/NewsSix";
import NewsSeven from "@/components/Block/NewsSeven";
import blogApiRequest from "@/apiRequests/blog";
import Image from 'next/image';
import backgroundImage from './background.jpg';
import { FadeIn, SlideInLeft, Stagger } from "@/components/Widget/AnimatedElements";
import { HomeBreadcrumb } from "@/components/Navigation/Breadcrumb";
import { Suspense } from "react";
import { NewsOneSkeleton, NewsFourSkeleton, CategorySkeleton } from "@/components/Widget/LoadingSkeleton";
import { Metadata } from "next";
import "@/styles/homepage.css";
import AdBanner from "@/components/Widget/AdBanner";

// SEO Metadata
export const metadata: Metadata = {
  title: "Tòa án Nhân dân TP.HCM - Cổng thông tin điện tử",
  description: "Cổng thông tin điện tử chính thức của Tòa án Nhân dân Thành phố Hồ Chí Minh. Cập nhật tin tức, thông báo, văn bản pháp luật và các hoạt động của tòa án.",
  keywords: "tòa án, TAND TPHCM, pháp luật, tin tức pháp luật, thông báo tòa án, văn bản pháp luật",
  authors: [{ name: "Tòa án Nhân dân TP.HCM" }],
  creator: "Tòa án Nhân dân TP.HCM",
  publisher: "Tòa án Nhân dân TP.HCM",
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  openGraph: {
    type: 'website',
    locale: 'vi_VN',
    url: 'https://tand.hochiminhcity.gov.vn',
    title: 'Tòa án Nhân dân TP.HCM - Cổng thông tin điện tử',
    description: 'Cổng thông tin điện tử chính thức của Tòa án Nhân dân Thành phố Hồ Chí Minh',
    siteName: 'Tòa án Nhân dân TP.HCM',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Tòa án Nhân dân TP.HCM - Cổng thông tin điện tử',
    description: 'Cổng thông tin điện tử chính thức của Tòa án Nhân dân Thành phố Hồ Chí Minh',
  },
  alternates: {
    canonical: 'https://tand.hochiminhcity.gov.vn',
  },
};

const componentMap: Record<number, React.ComponentType<any>> = {
  1: NewsOne,
  2: NewsTwo,
  3: NewsThree,
  4: NewsFour,
  5: NewsFive,
  6: NewsSix,
  7: NewsSeven,
};

export default async function Home() {
  const resPost = await blogApiRequest.fetchBlogHome();
  const allPostPerCat = resPost?.payload?.allPostPerCat ?? [];
  const newBlogs = resPost?.payload?.newBlogs ?? [];

  // Structured data for SEO
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "GovernmentOrganization",
    "name": "Tòa án Nhân dân Thành phố Hồ Chí Minh",
    "alternateName": "TAND TP.HCM",
    "url": "https://tand.hochiminhcity.gov.vn",
    "logo": "https://tand.hochiminhcity.gov.vn/favicon.ico",
    "description": "Cổng thông tin điện tử chính thức của Tòa án Nhân dân Thành phố Hồ Chí Minh",
    "address": {
      "@type": "PostalAddress",
      "addressLocality": "Thành phố Hồ Chí Minh",
      "addressCountry": "VN"
    },
    "sameAs": [
      "https://tand.hochiminhcity.gov.vn"
    ]
  };

  return (
    <>
      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />

      {/* Skip to content link for accessibility */}
      <a href="#main-content" className="skip-to-content">
        Bỏ qua đến nội dung chính
      </a>

      <main
        id="main-content"
        style={{
          backgroundImage: `url(${backgroundImage.src})`,
          backgroundSize: "cover",
          backgroundPosition: "center",
          backgroundAttachment: "fixed"
        }}
        className="min-h-screen py-2 px-2 sm:py-4 sm:px-4 relative"
        role="main"
        aria-label="Trang chủ Tòa án Nhân dân TP.HCM"
      >
        {/* Background overlay for better readability */}
        <div className="absolute inset-0 bg-black/5 pointer-events-none" aria-hidden="true"></div>

      <div className="container mx-auto relative z-10">
        {/* Breadcrumb Navigation */}
        <FadeIn delay={100}>
          <nav className="mb-4" aria-label="Breadcrumb">
            <HomeBreadcrumb />
          </nav>
        </FadeIn>

        {/* Enhanced Ad Banner - Only show if there's content */}
        <AdBanner />

        {/* Enhanced Top News Section */}
        <SlideInLeft delay={300}>
          <section className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4 md:gap-6 mb-6 sm:mb-8" aria-labelledby="featured-news">
            <article className="main-content col-span-1 md:col-span-3 bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 p-4 sm:p-6 transform hover:scale-[1.01]">
              <header className="w-full mb-6">
                <h1
                  id="featured-news"
                  className="text-lg sm:text-xl lg:text-2xl uppercase font-semibold text-white py-3 px-4 w-full rounded-lg shadow-md transition-all duration-300 hover:shadow-lg"
                  style={{
                    backgroundColor: '#e4393c',
                    background: 'linear-gradient(135deg, #e4393c 0%, #d32f2f 100%)'
                  }}
                >
                  <span className="flex items-center">
                    <span className="w-1 h-6 bg-white rounded-full mr-3 animate-pulse" aria-hidden="true"></span>
                    Tin Tức Nổi Bật
                  </span>
                </h1>
              </header>
              <div className="transition-all duration-300">
                <Suspense fallback={<NewsOneSkeleton />}>
                  <NewsOne blogs={newBlogs.slice(0, 4)} />
                </Suspense>
              </div>
            </article>

            {/* Enhanced Second block */}
            <aside className="col-span-1 bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 p-4 sm:p-6 transform hover:scale-[1.01]" aria-label="Tin tức khác">
              <div className="transition-all duration-300">
                <Suspense fallback={<NewsFourSkeleton />}>
                  <NewsFour blogs={newBlogs.slice(4, 8)} />
                </Suspense>
              </div>
            </aside>
          </section>
        </SlideInLeft>

        {/* Enhanced Category Sections */}
        <section className="grid auto-rows-auto grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6" aria-label="Các chuyên mục tin tức">
          {allPostPerCat.map((cat, index) => {
            if (!cat?.posts || !cat.block) return null;

            const BlogComponent = componentMap[cat.block];
            if (!BlogComponent) return null;

            let blogsSlice = cat.posts;
            switch (cat.block) {
              case 1:
              case 2:
                blogsSlice = cat.posts.slice(0, 7);
                break;
              case 3:
                blogsSlice = cat.posts.slice(0, 8);
                break;
              case 4:
                blogsSlice = cat.posts.slice(0, 4 + index);
                break;
              case 5:
                blogsSlice = cat.posts.slice(0, 3);
                break;
              case 6:
                blogsSlice = cat.posts.slice(0, 4);
                break;
              case 7:
                blogsSlice = cat.posts; // Full array
                break;
              default:
                blogsSlice = cat.posts;
            }

            // Special layout for certain blocks
            const isSpecialBlock = cat.block === 7;
            const specialClass = isSpecialBlock ? "md:col-span-2 lg:col-span-3" : "";

            return (
              <FadeIn
                key={`category-${cat.slug || index}`}
                delay={400 + (index * 150)}
                className={`bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 p-4 sm:p-6 transform hover:scale-[1.02] group ${specialClass}`}
              >
                <article
                  className="transition-all duration-300 group-hover:translate-y-[-2px]"
                  aria-labelledby={`category-title-${index}`}
                >
                  <Suspense fallback={<CategorySkeleton />}>
                    <BlogComponent
                      blogs={blogsSlice}
                      category={cat}
                    />
                  </Suspense>
                </article>
              </FadeIn>
            );
          })}
        </section>


      </div>
    </main>
    </>
  );
}
