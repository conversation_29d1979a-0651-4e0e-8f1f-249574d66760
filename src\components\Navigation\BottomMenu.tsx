import Link from "next/link";
import { useSetting } from "@/context/SettingContext";
import { ChevronDown } from "lucide-react";
import React, { useEffect, useState } from "react";
import { Home } from "react-feather";
import SearchNormal from "@/components/Navigation/SearchNormal";

const BottomMenu: React.FC = () => {
  const { menus } = useSetting();
  const [isSticky, setIsSticky] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      const navBar = document.getElementById("nav");
      if (!navBar) return;

      const navBottom = navBar.offsetTop + navBar.offsetHeight;
      const scrollPosition = window.scrollY || window.pageYOffset;

      if (scrollPosition >= navBottom) {
        setIsSticky(true);
      } else {
        setIsSticky(false);
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll); // Cleanup listener
  }, []);

  // Show skeleton if menus are not yet loaded
  if (!menus) {
    return (
      <div className="border-t border-red-800 bottom-nav text-white" style={{ backgroundColor: "#e4393c" }}>
        <div className="container mx-auto">
          <nav
            id="nav"
            className={`w-full ${
              isSticky ? "sticky top-0 shadow-md z-50" : ""
            }`}
            style={{ backgroundColor: "#e4393c" }}
          >
            <div className="flex w-full animate-pulse">
              {Array.from({ length: 6 }).map((_, i) => (
                <div key={i} className="flex-1 border-r border-red-800 last:border-r-0 h-10 py-2 px-4">
                  <div className="h-full bg-red-700 rounded"></div>
                </div>
              ))}
            </div>
          </nav>
        </div>
      </div>
    );
  }

  if (!Array.isArray(menus)) return null;

  const mainMenu = menus.find((menu: any) => menu.position === "0");
  if (!mainMenu || !mainMenu.obj) return null;

  const menuItems = mainMenu.obj.reduce((acc: any, item: any) => {
    acc[item.parent] = acc[item.parent] || [];
    acc[item.parent].push(item);
    return acc;
  }, {});

  const renderMenu = (parentId: number, isSubMenu = false) => {
    if (!menuItems[parentId]) return null;

    if (isSubMenu) {
      return (
        <ul className="sub-menu dropdown-content text-white z-[1] w-52 p-2 shadow rounded border-t border-red-700"
            style={{ backgroundColor: "#d13438" }}>
          {menuItems[parentId].map((item: any) => (
            <li key={item.id} className="relative my-2 cursor-pointer hover:bg-red-800">
              {menuItems[item.id] ? (
                <div className="dropdown dropdown-bottom dropdown-hover">
                  <div tabIndex={0} className="cursor-pointer flex items-center">
                    <Link href={`/${item.slug}`} className="block w-full">
                      <span className="whitespace-nowrap uppercase">{item.text}</span>
                    </Link>
                    <ChevronDown className="w-4 h-4 text-white ml-1 hidden md:flex" size="12" />
                  </div>
                  {renderMenu(item.id, true)}
                </div>
              ) : (
                <Link href={`/${item.slug}`} className="block w-full">
                  <span className="whitespace-nowrap uppercase">{item.text}</span>
                </Link>
              )}
            </li>
          ))}
        </ul>
      );
    }

    // For main menu, create a table-like structure with equal width cells
    return (
      <div className="flex w-full">
        {/* Home icon as first item in a box */}
        <Link href="/" className="flex-none border-r" style={{ backgroundColor: "#e4393c" }}>
          <div className="flex justify-center items-center h-full px-4 py-2">
            <Home size="18" className="text-white" />
          </div>
        </Link>
        
        {/* Menu items each in their own box */}
        {menuItems[parentId].map((item: any) => (
          <div 
            key={item.id} 
            className="text-center border-r"
            style={{ backgroundColor: "#e4393c" }}
          >
            {menuItems[item.id] ? (
              <div className="dropdown dropdown-bottom dropdown-hover h-full">
                <div tabIndex={0} className="h-full cursor-pointer flex justify-center items-center px-2 py-2 hover:bg-red-700">
                  <Link href={`/${item.slug}`} className="block">
                    <span className="whitespace-nowrap uppercase text-white text-sm font-medium">{item.text}</span>
                  </Link>
                  <ChevronDown className="w-4 h-4 text-white ml-1 hidden md:flex" size="12" />
                </div>
                {renderMenu(item.id, true)}
              </div>
            ) : (
              <Link href={`/${item.slug}`} className="block h-full">
                <div className="flex justify-center items-center h-full px-2 py-2 hover:bg-red-700">
                  <span className="whitespace-nowrap uppercase text-white text-sm font-medium">{item.text}</span>
                </div>
              </Link>
            )}
          </div>
        ))}
      </div>
    );
  };

  return (
    <div
      id="nav"
      className={`border-t border-red-800 bottom-nav ${
        isSticky ? "fixed top-0 shadow-md z-50 w-full" : ""
      }`}
      style={{ backgroundColor: "#e4393c" }}
    >
      <div
        className="container flex flex-nowrap items-center justify-between mx-auto px-0 md:px-2 overflow-x-scroll overflow-y-hidden md:overflow-visible"
        style={{ scrollbarWidth: "none", msOverflowStyle: "none" }}
      >
        <nav className={`w-full md:flex md:w-auto md:order-0`}>
          {renderMenu(0)}
        </nav>
        <span className="md:block hidden">
        <SearchNormal />
      </span>
      </div>
     
    </div>
  );
};

export default BottomMenu;
