import { NextRequest, NextResponse } from "next/server";

// GET - Get all departments (admin only)
export async function GET(request: NextRequest) {
  try {
    const token = request.headers.get("authorization")?.replace("Bearer ", "");
    if (!token) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    const backendUrl = process.env.BACKEND_URL || "http://localhost:8000";
    
    // Create an AbortController for timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

    let response: Response;
    try {
      response = await fetch(`${backendUrl}/api/departments`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${token}`,
        },
        cache: 'no-store',
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
    } catch (error: any) {
      clearTimeout(timeoutId);
      if (error.name === 'AbortError') {
        return NextResponse.json(
          { success: false, message: "Timeout: Không thể tải danh sách phòng ban" },
          { status: 408 }
        );
      }
      throw error;
    }

    const data = await response.json();

    if (!response.ok) {
      return NextResponse.json(
        { 
          success: false,
          message: data.message || "Lỗi server khi lấy danh sách phòng ban" 
        },
        { status: response.status }
      );
    }

    return NextResponse.json({
      success: true,
      data: data.data || [],
      message: data.message || "Lấy danh sách phòng ban thành công"
    });

  } catch (error) {
    // Console error removed
    return NextResponse.json(
      { success: false, message: "Không thể kết nối đến server" },
      { status: 500 }
    );
  }
}

// POST - Create new department (admin only)
export async function POST(request: NextRequest) {
  try {
    const token = request.headers.get("authorization")?.replace("Bearer ", "");
    if (!token) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { name } = body;

    if (!name || !name.trim()) {
      return NextResponse.json(
        { success: false, message: "Tên phòng ban không được để trống" },
        { status: 400 }
      );
    }

    const backendUrl = process.env.BACKEND_URL || "http://localhost:8000";
    
    // Create an AbortController for timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

    let response: Response;
    try {
      response = await fetch(`${backendUrl}/api/departments`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${token}`,
        },
        body: JSON.stringify({ name: name.trim() }),
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
    } catch (error: any) {
      clearTimeout(timeoutId);
      if (error.name === 'AbortError') {
        return NextResponse.json(
          { success: false, message: "Timeout: Không thể tạo phòng ban" },
          { status: 408 }
        );
      }
      throw error;
    }

    const data = await response.json();

    if (!response.ok) {
      return NextResponse.json(
        { 
          success: false,
          message: data.message || "Lỗi server khi tạo phòng ban" 
        },
        { status: response.status }
      );
    }

    return NextResponse.json({
      success: true,
      data: data.data,
      message: data.message || "Tạo phòng ban thành công"
    });

  } catch (error) {
    return NextResponse.json(
      { success: false, message: "Không thể kết nối đến server" },
      { status: 500 }
    );
  }
}
