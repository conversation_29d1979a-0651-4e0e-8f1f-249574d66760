"use client";

import { useState } from "react";

interface DocumentViewerProps {
  fileUrl: string;
  fileName: string;
  fileExt: string;
}

const DocumentViewer = ({ fileUrl, fileName, fileExt }: DocumentViewerProps) => {
  const [iframeError, setIframeError] = useState(false);
  const [loading, setLoading] = useState(true);

  // Google Docs viewer URL for better compatibility
  const googleViewerUrl = `https://docs.google.com/gview?embedded=true&url=${encodeURIComponent(fileUrl)}`;
  
  // Microsoft Office Online viewer for office documents
  const officeViewerUrl = `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(fileUrl)}`;

  const handleIframeLoad = () => {
    setLoading(false);
  };

  const handleIframeError = () => {
    setIframeError(true);
    setLoading(false);
  };

  // Determine best viewer based on file type
  const getViewerUrl = () => {
    if (fileExt === 'pdf') {
      return fileUrl; // Direct PDF viewing
    }
    
    if (['docx', 'doc', 'xlsx', 'xls', 'pptx', 'ppt'].includes(fileExt)) {
      // Try Google Docs viewer first, with Office Online as fallback
      return googleViewerUrl;
    }
    
    return googleViewerUrl; // Default fallback
  };

  const handleRetry = () => {
    setIframeError(false);
    setLoading(true);
  };

  if (iframeError) {
    return (
      <div className="border rounded-lg p-6 bg-gray-50">
        <div className="text-center">
          <div className="text-red-500 mb-2">⚠️</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Không thể xem trước tài liệu
          </h3>
          <p className="text-gray-600 mb-4">
            File này không thể hiển thị trực tiếp trong trình duyệt. 
            Vui lòng tải xuống để xem.
          </p>
          <div className="space-y-2">
            <p className="text-sm text-gray-500">File: {fileName}</p>
            <p className="text-sm text-gray-500">Loại: {fileExt.toUpperCase()}</p>
            <button
              onClick={handleRetry}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 text-sm"
            >
              Thử lại
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="relative border rounded-lg overflow-hidden">
      {loading && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 z-10">
          <div className="flex items-center space-x-2">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            <span className="text-gray-600">Đang tải tài liệu...</span>
          </div>
        </div>
      )}
      
      <iframe
        src={getViewerUrl()}
        className="w-full border-none h-screen"
        style={{ minHeight: '600px' }}
        onLoad={handleIframeLoad}
        onError={handleIframeError}
        title={`Document Viewer - ${fileName}`}
        sandbox="allow-same-origin allow-scripts allow-popups allow-forms"
      />
      
      <div className="absolute top-2 right-2 bg-black bg-opacity-50 text-white p-2 rounded text-xs">
        {fileName}
      </div>
    </div>
  );
};

export default DocumentViewer;
