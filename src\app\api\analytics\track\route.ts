import { NextRequest, NextResponse } from 'next/server';
import { connectDB } from '@/lib/mongodb';
import { PageView, DailyAnalytics } from '@/models/analytics';
import mongoose from 'mongoose';

// Update daily analytics
const updateDailyAnalytics = async (pageType: string, postId: string | null, sessionId: string) => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  try {
    let updateData: any = { $inc: { totalViews: 1 } };
    
    if (pageType === 'home') {
      updateData.$inc.homeViews = 1;
    } else if (pageType === 'post') {
      updateData.$inc.postViews = 1;
    }

    // Update or create daily analytics
    await DailyAnalytics.findOneAndUpdate(
      { date: today },
      updateData,
      { upsert: true, new: true }
    );

    // Update unique visitors count (approximate)
    const todayStart = new Date(today);
    const todayEnd = new Date(today);
    todayEnd.setDate(todayEnd.getDate() + 1);

    const uniqueSessionsToday = await PageView.distinct('sessionId', {
      timestamp: { $gte: todayStart, $lt: todayEnd }
    });

    await DailyAnalytics.findOneAndUpdate(
      { date: today },
      { uniqueVisitors: uniqueSessionsToday.length }
    );

    // Update top posts if this is a post view
    if (pageType === 'post' && postId) {
      // Try to find post by slug first, then by ID
      const Post = mongoose.models.Post || mongoose.model('Post', new mongoose.Schema({}, { strict: false }));
      let post;
      
      if (mongoose.Types.ObjectId.isValid(postId)) {
        post = await Post.findById(postId);
      } else {
        post = await Post.findOne({ slug: postId });
      }

      if (post) {
        const postObjectId = post._id;
        
        await DailyAnalytics.findOneAndUpdate(
          { date: today, 'topPosts.postId': postObjectId },
          { $inc: { 'topPosts.$.views': 1 } }
        );

        // If post not in topPosts, add it
        const analytics = await DailyAnalytics.findOne({ date: today });
        if (analytics && !analytics.topPosts.some((p: any) => p.postId.toString() === postObjectId.toString())) {
          await DailyAnalytics.findOneAndUpdate(
            { date: today },
            { $push: { topPosts: { postId: postObjectId, views: 1 } } }
          );
        }

        // Also increment post views counter in Post model
        await Post.findByIdAndUpdate(
          postObjectId,
          { $inc: { views: 1 } },
          { new: true }
        );
      }
    }

  } catch (error) {
    console.error('Daily analytics update error:', error);
  }
};

export async function POST(request: NextRequest) {
  try {
    await connectDB();

    const body = await request.json();
    const { page, path, postId, sessionId, userAgent, referer } = body;

    // Get client IP
    const forwarded = request.headers.get('x-forwarded-for');
    const ip = forwarded ? forwarded.split(',')[0] : request.headers.get('x-real-ip') || 'unknown';

    // Create page view record
    const pageView = new PageView({
      page,
      path,
      postId: postId || null,
      userAgent,
      ip,
      referer,
      sessionId,
      timestamp: new Date()
    });

    // Save asynchronously to not block response
    pageView.save().catch(err => {
      console.error('PageView save error:', err);
    });

    // Update daily analytics asynchronously
    updateDailyAnalytics(page, postId, sessionId).catch(err => {
      console.error('Daily analytics update error:', err);
    });

    return NextResponse.json({
      success: true,
      message: 'Page view tracked successfully'
    });

  } catch (error) {
    console.error('Analytics tracking error:', error);
    return NextResponse.json({
      success: false,
      message: 'Failed to track page view',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
