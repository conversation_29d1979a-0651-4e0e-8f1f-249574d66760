// Tệp: src/app/api/schedule/route.js
import { NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

// Đường dẫn đến file lưu trữ dữ liệu
const dataFilePath = path.join(process.cwd(), 'data', 'schedule-data.json');

// Đảm bảo thư mục data tồn tại
const ensureDirectoryExists = () => {
  const dir = path.join(process.cwd(), 'data');
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
};

// Hàm đọc dữ liệu từ file
const readDataFromFile = () => {
  ensureDirectoryExists();
  
  if (!fs.existsSync(dataFilePath)) {
    // Tạo file với dữ liệu mẫu nếu chưa tồn tại
    const sampleData = [
      {
        ngayLich: "10-Nov-22",
        buoi<PERSON>ich: "<PERSON>áng",
        loai<PERSON>n: "KTST",
        phong<PERSON>u: "4",
        nguyen<PERSON>on: "Ngân hàng TMCP Sài <PERSON> - <PERSON>",
        biDon: "Công ty Cổ phần đầu tư Xây dựng Trí Dũng",
        viecKien: "Tranh chấp hợp đồng tín dụng",
        hoTen: "Trương Thị Quỳnh Trâm",
        noiXu: "26 Lê Thánh Tôn, Quận 1"
      },
      {
        ngayLich: "15-Nov-22",
        buoiLich: "Sáng",
        loaiAn: "HCST",
        phongXu: "6",
        nguyenDon: "Nguyễn Thị Thanh Hà",
        biDon: "UBND huyện Củ Chi, TP.HCM",
        viecKien: "Khiếu kiện quyết định về cưỡng chế thu hồi đất",
        hoTen: "Nguyễn Minh Hiếu",
        noiXu: "26 Lê Thánh Tôn, Quận 1"
      }
    ];
    const initialData = {
      items: sampleData,
      lastUpdated: new Date().toISOString()
    };
    fs.writeFileSync(dataFilePath, JSON.stringify(initialData, null, 2));
    return initialData;
  }
  
  try {
    const fileContent = fs.readFileSync(dataFilePath, 'utf8');
    return JSON.parse(fileContent);
  } catch (error) {
    // Console error removed
    return { items: [], lastUpdated: null };
  }
};

// Hàm ghi dữ liệu vào file
const writeDataToFile = (data) => {
  ensureDirectoryExists();
  
  const dataToSave = {
    items: data,
    lastUpdated: new Date().toISOString()
  };
  
  try {
    fs.writeFileSync(dataFilePath, JSON.stringify(dataToSave, null, 2));
    return true;
  } catch (error) {
    // Console error removed
    return false;
  }
};

// API route để lấy dữ liệu
export async function GET() {
  try {
    const data = readDataFromFile();
    return NextResponse.json(data);
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to fetch data' },
      { status: 500 }
    );
  }
}

// API route để cập nhật dữ liệu
export async function POST(request) {
  try {
    const data = await request.json();
    
    if (!Array.isArray(data)) {
      return NextResponse.json(
        { error: 'Invalid data format' },
        { status: 400 }
      );
    }
    
    const success = writeDataToFile(data);
    
    if (success) {
      return NextResponse.json({
        success: true,
        message: 'Data updated successfully',
        lastUpdated: new Date().toISOString()
      });
    } else {
      return NextResponse.json(
        { error: 'Failed to save data' },
        { status: 500 }
      );
    }
  } catch (error) {
    // Console error removed
    return NextResponse.json(
      { error: 'Failed to update data' },
      { status: 500 }
    );
  }
}

// API route để xóa dữ liệu
export async function DELETE() {
  try {
    const success = writeDataToFile([]);
    
    if (success) {
      return NextResponse.json({
        success: true,
        message: 'All data deleted successfully',
        lastUpdated: new Date().toISOString()
      });
    } else {
      return NextResponse.json(
        { error: 'Failed to delete data' },
        { status: 500 }
      );
    }
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to delete data' },
      { status: 500 }
    );
  }
}
