const express = require('express');

const router = express.Router();
const passport = require('passport');
const menuController = require('../controllers/menu');
const { menuValidation, mongoIdValidation } = require('../middleware/inputValidation');


router.get("/libaries", passport.authenticate('user',{session: false}), menuController.getLibaries);

router.post('/', passport.authenticate('user', { session: false }), menuValidation, menuController.createMenu);

router.post("/get-all", passport.authenticate('user', { session: false }),  menuController.getMenu)

router.get("/:id", mongoIdValidation, menuController.getOneMenu)
  
router.put("/edit", (req, res, next) => {
  // Console log removed
  // Console log removed
  // Console log removed
  // Console log removed
  next();
}, passport.authenticate('user', { session: false }), menuController.editMenu);

router.delete("/:id", passport.authenticate('user', { session: false }), mongoIdValidation, menuController.deleteMenu) 


module.exports = router;
