"use client";

import { useState, useRef, useCallback } from "react";
import { Document, Page, pdfjs } from "react-pdf";
import { ChevronLeft, ChevronRight, ZoomIn, ZoomOut, Download, Maximize, RotateCw, Eye } from "lucide-react";

// Set up PDF.js worker
pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjs.version}/legacy/build/pdf.worker.min.mjs`;

interface PdfViewerProps {
  fileUrl: string;
}

function PdfViewer({ fileUrl }: PdfViewerProps) {
  const [numPages, setNumPages] = useState<number>(0);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [scale, setScale] = useState<number>(1.0);
  const [rotation, setRotation] = useState<number>(0);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string>("");
  const [showAllPages, setShowAllPages] = useState<boolean>(false);
  
  const containerRef = useRef<HTMLDivElement>(null);
  const fileCusUrl = "/api/pdf-proxy?url=" + encodeURIComponent(fileUrl);

  const onDocumentLoadSuccess = useCallback(({ numPages }: { numPages: number }) => {
    setNumPages(numPages);
    setIsLoading(false);
    setError("");
  }, []);

  const onDocumentLoadError = useCallback((error: Error) => {
    setError("Không thể tải file PDF. Vui lòng thử lại.");
    setIsLoading(false);
    // PDF load error handled silently
  }, []);

  const nextPage = () => {
    if (currentPage < numPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const prevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const zoomIn = () => {
    setScale(prevScale => Math.min(prevScale + 0.2, 3.0));
  };

  const zoomOut = () => {
    setScale(prevScale => Math.max(prevScale - 0.2, 0.5));
  };

  const rotate = () => {
    setRotation(prevRotation => (prevRotation + 90) % 360);
  };

  const toggleViewMode = () => {
    setShowAllPages(!showAllPages);
    if (!showAllPages) {
      setCurrentPage(1);
    }
  };

  const goToPage = (page: number) => {
    if (page >= 1 && page <= numPages) {
      setCurrentPage(page);
    }
  };

  if (error) {
    return (
      <div className="flex items-center justify-center p-8 border-2 border-dashed border-gray-300 rounded-lg bg-gray-50">
        <div className="text-center">
          <div className="text-red-500 mb-2">
            <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <p className="text-gray-600">{error}</p>
          <a 
            href={fileUrl} 
            target="_blank" 
            rel="noopener noreferrer"
            className="inline-flex items-center mt-3 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
          >
            <Download className="w-4 h-4 mr-2" />
            Tải xuống PDF
          </a>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full border rounded-lg shadow-lg bg-white overflow-hidden">
      {/* Toolbar */}
      <div className="bg-gray-100 border-b px-4 py-3 flex items-center justify-between flex-wrap gap-2">
        <div className="flex items-center space-x-2">
          <button
            onClick={prevPage}
            disabled={currentPage <= 1 || showAllPages}
            className="p-2 rounded bg-white border hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <ChevronLeft className="w-4 h-4" />
          </button>
          
          <div className="flex items-center space-x-2">
            <input
              type="number"
              value={currentPage}
              onChange={(e) => goToPage(parseInt(e.target.value))}
              min={1}
              max={numPages}
              disabled={showAllPages}
              className="w-16 px-2 py-1 text-center border rounded disabled:opacity-50"
            />
            <span className="text-sm text-gray-600">/ {numPages}</span>
          </div>

          <button
            onClick={nextPage}
            disabled={currentPage >= numPages || showAllPages}
            className="p-2 rounded bg-white border hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <ChevronRight className="w-4 h-4" />
          </button>
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={toggleViewMode}
            className="p-2 rounded bg-white border hover:bg-gray-50 transition-colors"
            title={showAllPages ? "Xem từng trang" : "Xem tất cả trang"}
          >
            <Eye className="w-4 h-4" />
          </button>

          <button
            onClick={zoomOut}
            className="p-2 rounded bg-white border hover:bg-gray-50 transition-colors"
            title="Thu nhỏ"
          >
            <ZoomOut className="w-4 h-4" />
          </button>
          
          <span className="text-sm text-gray-600 min-w-[60px] text-center">
            {Math.round(scale * 100)}%
          </span>
          
          <button
            onClick={zoomIn}
            className="p-2 rounded bg-white border hover:bg-gray-50 transition-colors"
            title="Phóng to"
          >
            <ZoomIn className="w-4 h-4" />
          </button>

          <button
            onClick={rotate}
            className="p-2 rounded bg-white border hover:bg-gray-50 transition-colors"
            title="Xoay"
          >
            <RotateCw className="w-4 h-4" />
          </button>

          <a
            href={fileUrl}
            target="_blank"
            rel="noopener noreferrer"
            className="p-2 rounded bg-blue-600 text-white hover:bg-blue-700 transition-colors"
            title="Tải xuống"
          >
            <Download className="w-4 h-4" />
          </a>
        </div>
      </div>

      {/* PDF Content */}
      <div 
        ref={containerRef}
        className="bg-gray-200 overflow-auto flex justify-center"
        style={{ minHeight: '850px', maxHeight: '1000px' }}
      >
        {isLoading && (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Đang tải PDF...</p>
            </div>
          </div>
        )}

        <div className="flex flex-col items-center py-4 w-full">
          <Document 
            file={fileCusUrl} 
            onLoadSuccess={onDocumentLoadSuccess}
            onLoadError={onDocumentLoadError}
            loading=""
            className="max-w-full"
          >
            {showAllPages ? (
              // Show all pages
              numPages > 0 && Array.from({ length: numPages }, (_, index) => (
                <div key={index} className="mb-4 shadow-lg max-w-full">
                  <Page 
                    pageNumber={index + 1}
                    scale={scale}
                    rotate={rotation}
                    renderTextLayer={false}
                    renderAnnotationLayer={false}
                    className="mx-auto max-w-full h-auto"
                  />
                </div>
              ))
            ) : (
              // Show single page
              numPages > 0 && (
                <div className="shadow-lg max-w-full">
                  <Page 
                    pageNumber={currentPage}
                    scale={scale}
                    rotate={rotation}
                    renderTextLayer={false}
                    renderAnnotationLayer={false}
                    className="mx-auto max-w-full h-auto"
                  />
                </div>
              )
            )}
          </Document>
        </div>
      </div>
      
      {/* Status Bar */}
      <div className="bg-gray-50 border-t px-4 py-2 text-sm text-gray-600 flex justify-between items-center">
        <span>
          {showAllPages ? `Hiển thị tất cả ${numPages} trang` : `Trang ${currentPage} / ${numPages}`}
        </span>
        <span>Tỷ lệ: {Math.round(scale * 100)}%</span>
      </div>
    </div>
  );
}

export default PdfViewer;
