"use client";

import React, { Component, ErrorInfo } from 'react';

interface FileViewerErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

interface FileViewerErrorBoundaryProps {
  children: React.ReactNode;
  fileName?: string;
  fallback?: React.ReactNode;
}

class FileViewerErrorBoundary extends React.Component<
  FileViewerErrorBoundaryProps,
  FileViewerErrorBoundaryState
> {
  constructor(props: FileViewerErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): FileViewerErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // File viewer error handled silently
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className="border rounded-lg p-6 bg-red-50 border-red-200">
          <div className="text-center">
            <div className="text-red-500 mb-2">⚠️</div>
            <h3 className="text-lg font-medium text-red-900 mb-2">
              Lỗi hiển thị file
            </h3>
            <p className="text-red-700 mb-4">
              Có lỗi xảy ra khi hiển thị file {this.props.fileName || 'này'}. 
              Vui lòng thử tải xuống file để xem.
            </p>
            <details className="text-left text-sm text-red-600">
              <summary className="cursor-pointer">Chi tiết lỗi</summary>
              <pre className="mt-2 p-2 bg-red-100 rounded text-xs overflow-auto">
                {this.state.error?.message}
              </pre>
            </details>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default FileViewerErrorBoundary;
