"use client";

import { useEffect } from 'react';
import { usePathname } from 'next/navigation';

// Generate session ID from browser fingerprint
const generateSessionId = () => {
  const today = new Date().toDateString();
  const userAgent = navigator.userAgent;
  const screen = `${window.screen.width}x${window.screen.height}`;
  const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
  
  const fingerprint = `${userAgent}-${screen}-${timezone}-${today}`;
  
  // Simple hash function
  let hash = 0;
  for (let i = 0; i < fingerprint.length; i++) {
    const char = fingerprint.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32bit integer
  }
  
  return Math.abs(hash).toString(36);
};

// Determine page type from path
const getPageType = (path: string) => {
  if (path === '/' || path === '/home') return 'home';
  if (path.startsWith('/blog/') && path !== '/blog') return 'post';
  if (path.startsWith('/category/')) return 'category';
  if (path.startsWith('/search')) return 'search';
  if (path === '/about') return 'about';
  if (path === '/contact') return 'contact';
  return 'other';
};

// Extract post ID from path
const extractPostId = (path: string) => {
  if (path.startsWith('/blog/')) {
    const slug = path.replace('/blog/', '');
    return slug; // We'll use slug as identifier
  }
  return null;
};

// Track page view
const trackPageView = async (path: string) => {
  try {
    const pageType = getPageType(path);
    const postId = extractPostId(path);
    const sessionId = generateSessionId();
    
    const data = {
      page: pageType,
      path,
      postId,
      sessionId,
      userAgent: navigator.userAgent,
      referer: document.referrer,
      timestamp: new Date().toISOString()
    };

    // Send to analytics API
    await fetch('/api/analytics/track', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
  } catch (error) {
    // Silently fail - don't break user experience
    console.warn('Analytics tracking failed:', error);
  }
};

// Custom hook for analytics
export const useAnalytics = () => {
  const pathname = usePathname();

  useEffect(() => {
    // Only track on client side
    if (typeof window === 'undefined') return;

    // Track page view with a small delay to ensure page is loaded
    const timer = setTimeout(() => {
      trackPageView(pathname);
    }, 1000);

    return () => clearTimeout(timer);
  }, [pathname]);

  return {
    trackEvent: (eventType: string, eventData?: any) => {
      // For future custom event tracking
      fetch('/api/analytics/event', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          eventType,
          eventData,
          timestamp: new Date().toISOString(),
          sessionId: generateSessionId(),
          path: pathname
        }),
      }).catch(error => {
        console.warn('Event tracking failed:', error);
      });
    }
  };
};
