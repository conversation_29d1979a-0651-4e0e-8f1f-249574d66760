"use client";

import React from 'react';
import Link from 'next/link';
import { ChevronRight } from 'react-feather';

interface BreadcrumbItem {
  label: string;
  href?: string;
  isActive?: boolean;
}

interface BreadcrumbProps {
  items: BreadcrumbItem[];
  className?: string;
}

const Breadcrumb: React.FC<BreadcrumbProps> = ({ items, className = '' }) => {
  // Don't render breadcrumb if no items
  if (!items || items.length === 0) {
    return null;
  }

  return (
    <nav
      className={`flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-300 ${className}`}
      aria-label="Breadcrumb"
    >
      {/* Home link without icon */}
      <Link
        href="/"
        className="hover:text-red-600 transition-colors duration-200"
        aria-label="Trang chủ"
      >
        Trang chủ
      </Link>

      {items.map((item, index) => (
        <React.Fragment key={index}>
          <ChevronRight className="w-4 h-4 text-gray-400" />

          {item.href && !item.isActive ? (
            <Link
              href={item.href}
              className="hover:text-red-600 transition-colors duration-200 truncate max-w-xs"
              title={item.label}
            >
              {item.label}
            </Link>
          ) : (
            <span
              className={`truncate max-w-xs ${
                item.isActive
                  ? 'text-red-600 font-medium'
                  : 'text-gray-500 dark:text-gray-400'
              }`}
              title={item.label}
            >
              {item.label}
            </span>
          )}
        </React.Fragment>
      ))}
    </nav>
  );
};

// Utility function to generate breadcrumb items from pathname
export const generateBreadcrumbItems = (
  pathname: string, 
  categoryName?: string, 
  postTitle?: string
): BreadcrumbItem[] => {
  const items: BreadcrumbItem[] = [];
  const segments = pathname.split('/').filter(Boolean);
  
  segments.forEach((segment, index) => {
    const isLast = index === segments.length - 1;
    
    // Handle different route patterns
    if (segment === 'blog') {
      if (isLast) {
        items.push({
          label: postTitle || 'Bài viết',
          isActive: true
        });
      } else {
        items.push({
          label: 'Tin tức',
          href: '/'
        });
      }
    } else if (segment === 'search') {
      items.push({
        label: 'Tìm kiếm',
        isActive: isLast
      });
    } else if (segment === 'page') {
      items.push({
        label: 'Trang',
        isActive: isLast
      });
    } else if (segment === 'author') {
      items.push({
        label: 'Tác giả',
        isActive: isLast
      });
    } else {
      // Category or other segments
      const href = isLast ? undefined : `/${segments.slice(0, index + 1).join('/')}`;
      items.push({
        label: categoryName || decodeURIComponent(segment),
        href,
        isActive: isLast
      });
    }
  });
  
  return items;
};

// Specific breadcrumb components for different pages
export const HomeBreadcrumb: React.FC = () => {
  // Don't show breadcrumb on home page
  return null;
};

export const CategoryBreadcrumb: React.FC<{ categoryName: string; categorySlug: string }> = ({ 
  categoryName, 
  categorySlug 
}) => (
  <Breadcrumb 
    items={[
      {
        label: categoryName,
        isActive: true
      }
    ]} 
  />
);

export const PostBreadcrumb: React.FC<{ 
  categoryName?: string; 
  categorySlug?: string; 
  postTitle: string 
}> = ({ categoryName, categorySlug, postTitle }) => {
  const items: BreadcrumbItem[] = [];
  
  if (categoryName && categorySlug) {
    items.push({
      label: categoryName,
      href: `/${categorySlug}`
    });
  }
  
  items.push({
    label: postTitle,
    isActive: true
  });
  
  return <Breadcrumb items={items} />;
};

export const SearchBreadcrumb: React.FC<{ query?: string }> = ({ query }) => (
  <Breadcrumb 
    items={[
      {
        label: query ? `Tìm kiếm: "${query}"` : 'Tìm kiếm',
        isActive: true
      }
    ]} 
  />
);

export const AuthorBreadcrumb: React.FC<{ authorName: string }> = ({ authorName }) => (
  <Breadcrumb 
    items={[
      {
        label: `Tác giả: ${authorName}`,
        isActive: true
      }
    ]} 
  />
);

export default Breadcrumb;
