import { NextRequest, NextResponse } from 'next/server';
import { connectDB } from '@/lib/mongodb';
import { PageView, DailyAnalytics } from '@/models/analytics';

export async function GET(request: NextRequest) {
  try {
    console.log("Analytics overview API called");
    await connectDB();

    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    const lastWeek = new Date(today);
    lastWeek.setDate(lastWeek.getDate() - 7);
    
    const lastMonth = new Date(today);
    lastMonth.setDate(lastMonth.getDate() - 30);

    // Get today's analytics
    const todayAnalytics = await DailyAnalytics.findOne({ date: today });
    
    // Get yesterday's analytics for comparison
    const yesterdayAnalytics = await DailyAnalytics.findOne({ date: yesterday });

    // Calculate growth rates
    const homeViewsGrowth = yesterdayAnalytics?.homeViews 
      ? ((todayAnalytics?.homeViews || 0) - yesterdayAnalytics.homeViews) / yesterdayAnalytics.homeViews * 100
      : 0;
    
    const postViewsGrowth = yesterdayAnalytics?.postViews 
      ? ((todayAnalytics?.postViews || 0) - yesterdayAnalytics.postViews) / yesterdayAnalytics.postViews * 100
      : 0;

    // Get total views for different periods
    const [
      totalViewsAllTime,
      totalViewsThisMonth,
      totalViewsThisWeek,
      totalViewsToday,
      uniqueVisitorsToday,
      topPostsToday
    ] = await Promise.all([
      PageView.countDocuments(),
      PageView.countDocuments({ timestamp: { $gte: lastMonth } }),
      PageView.countDocuments({ timestamp: { $gte: lastWeek } }),
      PageView.countDocuments({ timestamp: { $gte: today } }),
      PageView.distinct('sessionId', { timestamp: { $gte: today } }).then(sessions => sessions.length),
      PageView.aggregate([
        { $match: { timestamp: { $gte: today }, page: 'post' } },
        { $group: { _id: '$postId', views: { $sum: 1 } } },
        { $sort: { views: -1 } },
        { $limit: 5 },
        { $lookup: { from: 'posts', localField: '_id', foreignField: '_id', as: 'post' } },
        { $unwind: '$post' },
        { $project: { postId: '$_id', views: 1, title: '$post.title', slug: '$post.slug' } }
      ])
    ]);

    return NextResponse.json({
      success: true,
      analytics: {
        overview: {
          totalViewsAllTime,
          totalViewsThisMonth,
          totalViewsThisWeek,
          totalViewsToday,
          homeViewsToday: todayAnalytics?.homeViews || 0,
          postViewsToday: todayAnalytics?.postViews || 0,
          uniqueVisitorsToday,
          homeViewsGrowth: Math.round(homeViewsGrowth * 100) / 100,
          postViewsGrowth: Math.round(postViewsGrowth * 100) / 100
        },
        topPostsToday
      }
    });
  } catch (error) {
    console.error('Analytics overview error:', error);
    return NextResponse.json({
      success: false,
      message: "Cannot get analytics overview",
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
