"use client";
import { useEffect, useState, use } from "react";
import AddForm from "@/components/Form/AddBlog";
import { BlogRes, BlogUserCreate } from "@/schemaValidations/blog.schema";
import blogApiRequest from "@/apiRequests/blog";
import { toast } from "react-toastify";
import { z } from "zod";
import { refreshAllVideos } from "@/utils/videoUtils";
import Revision from "@/components/Widget/Revision";
import { handleApiError } from "@/utils/errorHandler";

type BlogFormValues = z.infer<typeof BlogUserCreate>[0];

export default function EditBlog({ params }: { params: Promise<{ id: any }> }) {
  const [blog, setBlog] = useState<BlogFormValues | null>(null);
  const [sessionToken, setSessionToken] = useState<string>("");
  const resolvedParams = use(params);
  const Id = resolvedParams.id;
  
  useEffect(() => {
    // Get session token on client side only
    const token = localStorage.getItem("sessionToken") || "";
    setSessionToken(token);
  }, []);

  useEffect(() => {
    if (!sessionToken) return; // Wait for session token to be loaded

    const fetchBlog = async () => {
      try {
        const result = await blogApiRequest.userFetchBlogById(Id, sessionToken);
        if (result.payload.success) {
          setBlog(result.payload.post);

          // Force refresh videos after blog data is loaded
          setTimeout(() => {
            refreshAllVideos();
          }, 1000);
        } else {
          toast.error("Error fetching blog.");
          console.error("Error fetching blog:", result.payload.message);
        }
      } catch (error) {
        console.error("Unexpected error:", error);
      }
    };

    if (Id) {
      fetchBlog();
    }
  }, [Id, sessionToken]);

  // Force refresh videos on page load
  useEffect(() => {
    const intervals = [500, 1000, 2000, 3000];
    const timeouts = intervals.map(delay =>
      setTimeout(() => {
        refreshAllVideos();
      }, delay)
    );

    return () => {
      timeouts.forEach(timeout => clearTimeout(timeout));
    };
  }, []);

  if (!blog) {
    return <p>Loading...</p>; // Ensure `blog` is loaded before rendering
  }
  const handleUpdate = async (data: BlogFormValues) => {
    console.log("handleUpdate called with data:", data);
    try {
      console.log("Making API call to userUpdateBlog...");
      const result = await blogApiRequest.userUpdateBlog(data, sessionToken);
      console.log("API response:", result);

      if (result.payload.success) {
        console.log("Updated blog data received:", result.payload.post);
        console.log("Updated blog file field:", result.payload.post.file);
        console.log("Updated blog video field:", result.payload.post.video);
        setBlog(result.payload.post);
        toast.success("Thành Công");
        console.log("Blog updated successfully");
      } else {
        console.error("API returned error:", result.payload.message);
        handleApiError(result, toast, "Có lỗi xảy ra khi cập nhật blog. Vui lòng thử lại.");
      }
    } catch (error) {
      console.error("Unexpected error in handleUpdate:", error);
      handleApiError(error, toast, "Có lỗi xảy ra khi cập nhật blog. Vui lòng thử lại.");
    }
  };

  return (
    <>
      <h1 className="text-2xl"> Blog</h1>
      <AddForm onSubmit={handleUpdate} blog={blog} />
      {(blog as any).revisions && (blog as any).updatedAt && (
        <Revision post={blog as any} />
      )}
    </>
  );
}
