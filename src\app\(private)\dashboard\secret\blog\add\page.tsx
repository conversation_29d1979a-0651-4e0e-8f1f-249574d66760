"use client";
import AddForm from "@/app/(private)/dashboard/secret/blog/add/add-form";
import blogApiRequest from "@/apiRequests/blog";
import { BlogCreateType } from "@/schemaValidations/blog.schema"; 
import { toast } from "react-toastify";
import { useRouter } from "next/navigation";
import { handleApiError } from "@/utils/errorHandler";



const AddBlog = () => {
  const router = useRouter();
  const handleCreate = async (data:  BlogCreateType) => {
  
    try {
      // Validate that required fields are present
      if (!data.title || !data.slug) {
        toast.error("Vui lòng điền đầy đủ tiêu đề và slug");
        return;
      }
      
      if (!data.categories || data.categories.length === 0) {
        toast.error("Vui lòng chọn ít nhất một danh mục");
        return;
      }

      // Clean up the data before sending
      const cleanData = {
        ...data,
        // Ensure file and video are properly separated
        file: data.file || null,
        video: data.video || null,
      };

      // Log the data being sent for debugging
      // Console log removed

      const sessionToken = localStorage.getItem("sessionToken") || "";
      const result = await blogApiRequest.createBlog(cleanData, sessionToken);
      if (result.payload.success) {
        toast.success("Thành Công");
        router.push(`/dashboard/secret/blog/${result.payload.post._id}`);
      } else {
        handleApiError(result, toast, "Có lỗi xảy ra khi tạo blog. Vui lòng thử lại.");
        // Console error removed
      }
    } catch (error) {
      // Console error removed
      handleApiError(error, toast, "Có lỗi xảy ra khi tạo blog. Vui lòng thử lại.");
    }
  };
    return (
      <>
        <h1 className="text-2xl"> Blog</h1>
        <AddForm onSubmit={handleCreate} />
      </>
    );
  };
  
  export default AddBlog;
  
