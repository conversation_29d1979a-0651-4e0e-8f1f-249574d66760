"use client";

import React, { useState, useEffect } from 'react';
import { X, Calendar, User, Eye, Tag, ExternalLink } from 'react-feather';
import adminApiRequest, { PendingPost } from '@/apiRequests/admin';
import { toast } from 'react-toastify';

interface PendingPostsModalProps {
  isOpen: boolean;
  onClose: () => void;
  sessionToken: string;
}

const PendingPostsModal: React.FC<PendingPostsModalProps> = ({
  isOpen,
  onClose,
  sessionToken
}) => {
  const [posts, setPosts] = useState<PendingPost[]>([]);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalPosts, setTotalPosts] = useState(0);

  useEffect(() => {
    // Console log removed
    if (isOpen && sessionToken) {
      // Console log removed
      fetchPendingPosts(1);
    }
  }, [isOpen, sessionToken]);

  const fetchPendingPosts = async (page: number) => {
    try {
      setLoading(true);
      const result = await adminApiRequest.getPendingPosts(sessionToken, page, 10);
      
      if (result.payload.success) {
        setPosts(result.payload.posts);
        setCurrentPage(result.payload.pagination.currentPage);
        setTotalPages(result.payload.pagination.totalPages);
        setTotalPosts(result.payload.pagination.totalPosts);
      } else {
        toast.error("Không thể tải danh sách bài viết chờ kích hoạt");
      }
    } catch (error: any) {
      // Console error removed
      toast.error("Lỗi khi tải danh sách bài viết");
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('vi-VN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      fetchPendingPosts(page);
    }
  };

  // Console log removed

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Bài viết chờ kích hoạt</h2>
            <p className="text-sm text-gray-600 mt-1">
              Tổng cộng {totalPosts} bài viết đang chờ được kích hoạt
            </p>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X size={24} className="text-gray-500" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          {loading ? (
            // Loading skeleton
            <div className="space-y-4">
              {Array.from({ length: 5 }).map((_, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4 animate-pulse">
                  <div className="h-6 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
                  <div className="flex space-x-4">
                    <div className="h-4 bg-gray-200 rounded w-24"></div>
                    <div className="h-4 bg-gray-200 rounded w-32"></div>
                    <div className="h-4 bg-gray-200 rounded w-20"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : posts.length > 0 ? (
            <div className="space-y-4">
              {posts.map((post) => (
                <div key={post._id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">
                        {post.title}
                      </h3>
                      <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                        {post.short}
                      </p>
                      
                      <div className="flex flex-wrap items-center gap-4 text-sm text-gray-500">
                        <div className="flex items-center gap-1">
                          <User size={16} />
                          <span>{post.user.username}</span>
                        </div>
                        
                        <div className="flex items-center gap-1">
                          <Calendar size={16} />
                          <span>{formatDate(post.createdAt)}</span>
                        </div>
                        
                        {post.views > 0 && (
                          <div className="flex items-center gap-1">
                            <Eye size={16} />
                            <span>{post.views} lượt xem</span>
                          </div>
                        )}
                        
                        {post.categories.length > 0 && (
                          <div className="flex items-center gap-1">
                            <Tag size={16} />
                            <span>{post.categories[0].name}</span>
                            {post.categories.length > 1 && (
                              <span className="text-xs bg-gray-100 px-2 py-1 rounded">
                                +{post.categories.length - 1}
                              </span>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2 ml-4">
                      <span className="px-3 py-1 bg-orange-100 text-orange-800 text-xs font-medium rounded-full">
                        Chờ kích hoạt
                      </span>
                      <a
                        href={`/dashboard/secret/blog/${post._id}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                        title="Xem chi tiết"
                      >
                        <ExternalLink size={16} />
                      </a>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <Calendar size={48} className="mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Không có bài viết chờ kích hoạt
              </h3>
              <p className="text-gray-500">
                Tất cả bài viết đã được kích hoạt hoặc chưa có bài viết nào.
              </p>
            </div>
          )}
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between px-6 py-4 border-t border-gray-200">
            <div className="text-sm text-gray-700">
              Trang {currentPage} / {totalPages} • {totalPosts} bài viết
            </div>
            <div className="flex items-center gap-2">
              <button
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
                className="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Trước
              </button>
              
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                const page = i + 1;
                return (
                  <button
                    key={page}
                    onClick={() => handlePageChange(page)}
                    className={`px-3 py-2 text-sm rounded-md ${
                      currentPage === page
                        ? 'bg-blue-600 text-white'
                        : 'border border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    {page}
                  </button>
                );
              })}
              
              <button
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
                className="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Sau
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PendingPostsModal;
