import http from "@/lib/http";
import { 
  FileResType, 
  FileDeleteType, 
  FileUploadType, 
  FileSearchType,
  FileBulkActionType 
} from "@/schemaValidations/file.schema";

const fileApiRequest = {
  // Get all files with pagination and filters
  getFiles: (params: FileSearchType, sessionToken: string) =>
    http.post<FileResType>(`/api/administrator/files`, params, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  // Get file by ID
  getFileById: (id: string, sessionToken: string) =>
    http.get<{ success: boolean; file: any }>(`/api/administrator/files/${id}`, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  // Delete single file
  deleteFile: (id: string, sessionToken: string) =>
    http.delete<FileDeleteType>(`/api/administrator/files/${id}`, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  // Bulk actions on files
  bulkAction: (data: FileBulkActionType, sessionToken: string) =>
    http.post<FileDeleteType>(`/api/administrator/files/bulk-action`, data, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  // Upload new file
  uploadFile: (formData: FormData, sessionToken: string) =>
    http.post<FileUploadType>(`/api/administrator/files/upload`, formData, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
        'Content-Type': 'multipart/form-data',
      },
    }),

  // Update file metadata
  updateFile: (id: string, data: { description?: string; tags?: string[]; isActive?: boolean }, sessionToken: string) =>
    http.put<{ success: boolean; file: any; message: string }>(`/api/administrator/files/${id}`, data, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  // Get file statistics
  getFileStats: (sessionToken: string) =>
    http.get<{
      success: boolean;
      stats: {
        totalFiles: number;
        totalSize: number;
        filesByType: { type: string; count: number; size: number }[];
        recentUploads: number;
      };
    }>(`/api/administrator/files/stats`, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  // Search files
  searchFiles: (query: string, sessionToken: string) =>
    http.get<FileResType>(`/api/administrator/files/search?q=${encodeURIComponent(query)}`, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  // Get sync status
  getSyncStatus: (sessionToken: string) =>
    http.get<{
      success: boolean;
      status: {
        totalPhysicalFiles: number;
        totalDatabaseFiles: number;
        missingInDatabase: number;
        missingFiles: Array<{
          relativePath: string;
          filename: string;
          size: number;
          mtime: Date;
        }>;
      };
    }>(`/api/administrator/files/sync-status`, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  // Sync existing files
  syncExistingFiles: (sessionToken: string) =>
    http.post<{ success: boolean; message: string }>(`/api/administrator/files/sync-existing`, {}, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),
};

export default fileApiRequest;
