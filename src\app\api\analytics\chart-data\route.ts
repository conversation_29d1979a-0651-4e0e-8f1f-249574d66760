import { NextRequest, NextResponse } from 'next/server';
import { connectDB } from '@/lib/mongodb';
import { DailyAnalytics } from '@/models/analytics';

export async function GET(request: NextRequest) {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const days = parseInt(searchParams.get('days') || '7');
    
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    startDate.setHours(0, 0, 0, 0);

    // Get daily analytics for the period
    const dailyData = await DailyAnalytics.find({
      date: { $gte: startDate }
    }).sort({ date: 1 });

    // Fill missing days with zero data
    const chartData = [];
    for (let i = 0; i < days; i++) {
      const date = new Date(startDate);
      date.setDate(date.getDate() + i);
      
      const dayData = dailyData.find(d => 
        d.date.toDateString() === date.toDateString()
      );
      
      chartData.push({
        date: date.toISOString().split('T')[0],
        homeViews: dayData?.homeViews || 0,
        postViews: dayData?.postViews || 0,
        totalViews: dayData?.totalViews || 0,
        uniqueVisitors: dayData?.uniqueVisitors || 0
      });
    }

    return NextResponse.json({
      success: true,
      chartData
    });
  } catch (error) {
    console.error('Analytics chart data error:', error);
    return NextResponse.json({
      success: false,
      message: "Cannot get analytics chart data",
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
