const Leader = require("../models/leader");

// Public API - Get active leaders only
const getPublicLeaders = async (req, res) => {
  try {
    const leaders = await Leader.find({ isActive: true })
      .populate('position', 'name description level')
      .populate('department', 'name level') // Include department level
      .sort({ order: 1, createdAt: -1 })
      .select('-__v');

    res.status(200).json({
      success: true,
      data: leaders,
      message: "Lấy danh sách lãnh đạo thành công"
    });
  } catch (error) {
    // Console error removed
    res.status(500).json({
      success: false,
      message: "Lỗi server khi lấy danh sách lãnh đạo"
    });
  }
};

// Admin API - Get all leaders
const getAllLeaders = async (req, res) => {
  try {
    const leaders = await Leader.find({})
      .populate('position', 'name description level')
      .populate('department', 'name')
      .sort({ order: 1, createdAt: -1 })
      .select('-__v');

    res.status(200).json({
      success: true,
      data: leaders,
      message: "<PERSON><PERSON><PERSON> danh sách lãnh đạo thành công"
    });
  } catch (error) {
    // Console error removed
    res.status(500).json({
      success: false,
      message: "Lỗi server khi lấy danh sách lãnh đạo"
    });
  }
};

// Admin API - Get single leader
const getLeader = async (req, res) => {
  try {
    const { id } = req.params;
    const leader = await Leader.findById(id)
      .populate('position', 'name description level')
      .populate('department', 'name')
      .select('-__v');

    if (!leader) {
      return res.status(404).json({
        success: false,
        message: "Không tìm thấy lãnh đạo"
      });
    }

    res.status(200).json({
      success: true,
      data: leader,
      message: "Lấy thông tin lãnh đạo thành công"
    });
  } catch (error) {
    // Console error removed
    res.status(500).json({
      success: false,
      message: "Lỗi server khi lấy thông tin lãnh đạo"
    });
  }
};

// Admin API - Create new leader
const createLeader = async (req, res) => {
  try {
    const {
      name,
      position,
      department,
      bio,
      experience,
      education,
      birthYear,
      hometown,
      title,
      achievements = [],
      phone,
      email,
      image,
      order = 1,
      isActive = true
    } = req.body;

    // Validate required fields
    if (!name || !position || !department) {
      return res.status(400).json({
        success: false,
        message: "Vui lòng điền đầy đủ thông tin bắt buộc (Tên, Chức vụ, Phòng ban)"
      });
    }

    // Check if order already exists and adjust if needed
    if (order) {
      const existingWithOrder = await Leader.findOne({ order });
      if (existingWithOrder) {
        // Increment order of existing leaders
        await Leader.updateMany(
          { order: { $gte: order } },
          { $inc: { order: 1 } }
        );
      }
    }

    const newLeader = new Leader({
      name,
      position,
      department,
      bio: bio || "",
      experience: experience || "",
      education: education || "",
      birthYear: birthYear || null,
      hometown: hometown || "",
      title: title || "",
      achievements,
      phone: phone || null,
      email: email || null,
      image: image || null,
      order,
      isActive
    });

    const savedLeader = await newLeader.save();

    res.status(201).json({
      success: true,
      data: savedLeader,
      message: "Tạo lãnh đạo mới thành công"
    });
  } catch (error) {
    // Console error removed
    
    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: "Tên lãnh đạo đã tồn tại"
      });
    }

    res.status(500).json({
      success: false,
      message: "Lỗi server khi tạo lãnh đạo mới"
    });
  }
};

// Admin API - Update leader
const updateLeader = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    // Remove undefined values
    Object.keys(updateData).forEach(key =>
      updateData[key] === undefined && delete updateData[key]
    );

    const leader = await Leader.findById(id);
    if (!leader) {
      return res.status(404).json({
        success: false,
        message: "Không tìm thấy lãnh đạo"
      });
    }

    // Handle order change
    if (updateData.order && updateData.order !== leader.order) {
      const newOrder = updateData.order;
      const oldOrder = leader.order;

      if (newOrder > oldOrder) {
        // Moving down: decrease order of items between old and new position
        await Leader.updateMany(
          { order: { $gt: oldOrder, $lte: newOrder }, _id: { $ne: id } },
          { $inc: { order: -1 } }
        );
      } else {
        // Moving up: increase order of items between new and old position
        await Leader.updateMany(
          { order: { $gte: newOrder, $lt: oldOrder }, _id: { $ne: id } },
          { $inc: { order: 1 } }
        );
      }
    }

    const updatedLeader = await Leader.findByIdAndUpdate(
      id,
      updateData,
      { new: true, runValidators: true }
    ).select('-__v');

    res.status(200).json({
      success: true,
      data: updatedLeader,
      message: "Cập nhật thông tin lãnh đạo thành công"
    });
  } catch (error) {
    // Console error removed
    
    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: "Tên lãnh đạo đã tồn tại"
      });
    }

    res.status(500).json({
      success: false,
      message: "Lỗi server khi cập nhật thông tin lãnh đạo"
    });
  }
};

// Admin API - Delete leader
const deleteLeader = async (req, res) => {
  try {
    const { id } = req.params;

    const leader = await Leader.findById(id);
    if (!leader) {
      return res.status(404).json({
        success: false,
        message: "Không tìm thấy lãnh đạo"
      });
    }

    // Adjust order of remaining leaders
    await Leader.updateMany(
      { order: { $gt: leader.order } },
      { $inc: { order: -1 } }
    );

    await Leader.findByIdAndDelete(id);

    res.status(200).json({
      success: true,
      message: "Xóa lãnh đạo thành công"
    });
  } catch (error) {
    // Console error removed
    res.status(500).json({
      success: false,
      message: "Lỗi server khi xóa lãnh đạo"
    });
  }
};

// Admin API - Toggle leader active status
const toggleLeaderStatus = async (req, res) => {
  try {
    const { id } = req.params;

    const leader = await Leader.findById(id);
    if (!leader) {
      return res.status(404).json({
        success: false,
        message: "Không tìm thấy lãnh đạo"
      });
    }

    leader.isActive = !leader.isActive;
    await leader.save();

    res.status(200).json({
      success: true,
      data: leader,
      message: `${leader.isActive ? 'Hiển thị' : 'Ẩn'} lãnh đạo thành công`
    });
  } catch (error) {
    // Console error removed
    res.status(500).json({
      success: false,
      message: "Lỗi server khi thay đổi trạng thái lãnh đạo"
    });
  }
};

// Admin API - Reorder leaders
const reorderLeaders = async (req, res) => {
  try {
    const { leaders } = req.body;

    if (!Array.isArray(leaders)) {
      return res.status(400).json({
        success: false,
        message: "Dữ liệu không hợp lệ"
      });
    }

    // Update each leader's order
    const updatePromises = leaders.map(({ id, order }) =>
      Leader.findByIdAndUpdate(id, { order }, { new: true })
    );

    await Promise.all(updatePromises);

    res.status(200).json({
      success: true,
      message: "Cập nhật thứ tự lãnh đạo thành công"
    });
  } catch (error) {
    // Console error removed
    res.status(500).json({
      success: false,
      message: "Lỗi server khi cập nhật thứ tự lãnh đạo"
    });
  }
};

module.exports = {
  getPublicLeaders,
  getAllLeaders,
  getLeader,
  createLeader,
  updateLeader,
  deleteLeader,
  toggleLeaderStatus,
  reorderLeaders
};
